#!/bin/bash

# SendRefMsg测试编译脚本

echo "编译SendRefMsg测试..."

# 设置编译参数
CXX=g++
CXXFLAGS="-std=c++17 -Wall -Wextra -g -O0"
INCLUDES="-I../Common -I../Protocol -I../GameEngine"

# 编译对象文件
echo "编译BaseObject.cpp..."
$CXX $CXXFLAGS $INCLUDES -c BaseObject.cpp -o BaseObject.o

if [ $? -ne 0 ]; then
    echo "BaseObject.cpp编译失败"
    exit 1
fi

echo "编译Utils.cpp..."
$CXX $CXXFLAGS $INCLUDES -c ../Common/Utils.cpp -o Utils.o

if [ $? -ne 0 ]; then
    echo "Utils.cpp编译失败"
    exit 1
fi

echo "编译Logger.cpp..."
$CXX $CXXFLAGS $INCLUDES -c ../Common/Logger.cpp -o Logger.o

if [ $? -ne 0 ]; then
    echo "Logger.cpp编译失败"
    exit 1
fi

echo "编译Environment.cpp..."
$CXX $CXXFLAGS $INCLUDES -c ../GameEngine/Environment.cpp -o Environment.o

if [ $? -ne 0 ]; then
    echo "Environment.cpp编译失败"
    exit 1
fi

echo "编译测试文件..."
$CXX $CXXFLAGS $INCLUDES -c SendRefMsgTest.cpp -o SendRefMsgTest.o

if [ $? -ne 0 ]; then
    echo "SendRefMsgTest.cpp编译失败"
    exit 1
fi

echo "链接可执行文件..."
$CXX $CXXFLAGS BaseObject.o Utils.o Logger.o Environment.o SendRefMsgTest.o -o SendRefMsgTest

if [ $? -ne 0 ]; then
    echo "链接失败"
    exit 1
fi

echo "编译成功！可执行文件: SendRefMsgTest"
echo "运行测试: ./SendRefMsgTest"
