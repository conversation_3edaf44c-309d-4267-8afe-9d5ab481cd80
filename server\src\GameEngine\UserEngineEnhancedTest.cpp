// UserEngineEnhancedTest.cpp - UserEngine完善功能测试
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "PKManager.h"
#include "GroupManager.h"
#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>
#include <cassert>
#include <thread>
#include <chrono>

using namespace MirServer;

class UserEngineEnhancedTest {
public:
    static void RunAllTests() {
        std::cout << "=== UserEngine完善功能测试开始 ===" << std::endl;
        
        TestHandleMethodsIntegration();
        TestItemHandling();
        TestMovementHandling();
        TestCombatHandling();
        TestMagicHandling();
        TestTradeHandling();
        TestQuestHandling();
        TestEnvironmentUpdates();
        TestPerformance();
        
        std::cout << "=== UserEngine完善功能测试完成 ===" << std::endl;
    }

private:
    static void TestHandleMethodsIntegration() {
        std::cout << "\n--- 测试Handle方法集成 ---" << std::endl;
        
        try {
            // 创建管理器实例
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            auto magicManager = std::make_shared<MagicManager>();
            auto storageManager = std::make_shared<StorageManager>();
            auto tradeManager = std::make_shared<TradeManager>();
            auto questManager = std::make_shared<QuestManager>();
            auto miniMapManager = std::make_shared<MiniMapManager>();
            auto repairManager = std::make_shared<RepairManager>();

            // 初始化管理器
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            magicManager->Initialize();
            storageManager->Initialize();
            tradeManager->Initialize();
            questManager->Initialize();
            miniMapManager->Initialize();
            repairManager->Initialize();

            // 初始化UserEngine
            bool result = userEngine->Initialize(mapManager, itemManager, magicManager,
                                               storageManager, tradeManager, questManager,
                                               miniMapManager, repairManager);
            assert(result == true);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "TestPlayer";
            humData.job = JobType::WARRIOR;
            humData.gender = GenderType::MALE;
            humData.level = 10;
            humData.currentPos = {100, 100};
            humData.mapName = "3";
            humData.gold = 1000;

            auto player = userEngine->CreatePlayer(humData);
            assert(player != nullptr);

            // 测试玩家添加
            result = userEngine->AddPlayer(player);
            assert(result == true);

            // 测试ProcessPlayers方法（包含所有Handle方法）
            userEngine->ProcessPlayers();

            std::cout << "✓ Handle方法集成测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ Handle方法集成测试失败: " << e.what() << std::endl;
        }
    }

    static void TestItemHandling() {
        std::cout << "\n--- 测试物品处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto itemManager = std::make_shared<ItemManager>();
            auto mapManager = std::make_shared<MapManager>();
            
            itemManager->Initialize("./data");
            mapManager->Initialize("./maps");
            userEngine->Initialize(mapManager, itemManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "ItemTestPlayer";
            humData.level = 20;
            humData.abil.HP = 100;
            humData.abil.MP = 50;

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试物品处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 物品处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 物品处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestMovementHandling() {
        std::cout << "\n--- 测试移动处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            userEngine->Initialize(mapManager, itemManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "MoveTestPlayer";
            humData.currentPos = {150, 150};
            humData.mapName = "3";

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试移动处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 移动处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 移动处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestCombatHandling() {
        std::cout << "\n--- 测试战斗处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            userEngine->Initialize(mapManager, itemManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "CombatTestPlayer";
            humData.pkPoint = 100; // 红名玩家

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试战斗处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 战斗处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 战斗处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestMagicHandling() {
        std::cout << "\n--- 测试魔法处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            auto magicManager = std::make_shared<MagicManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            magicManager->Initialize();
            userEngine->Initialize(mapManager, itemManager, magicManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "MagicTestPlayer";
            humData.abil.MP = 80;

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试魔法处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 魔法处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 魔法处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestTradeHandling() {
        std::cout << "\n--- 测试交易处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            auto tradeManager = std::make_shared<TradeManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            tradeManager->Initialize();
            userEngine->Initialize(mapManager, itemManager, nullptr, nullptr, tradeManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "TradeTestPlayer";

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试交易处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 交易处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 交易处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestQuestHandling() {
        std::cout << "\n--- 测试任务处理功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            auto questManager = std::make_shared<QuestManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            questManager->Initialize();
            userEngine->Initialize(mapManager, itemManager, nullptr, nullptr, nullptr, questManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "QuestTestPlayer";

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试任务处理
            userEngine->ProcessPlayers();

            std::cout << "✓ 任务处理功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 任务处理功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestEnvironmentUpdates() {
        std::cout << "\n--- 测试环境更新功能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            userEngine->Initialize(mapManager, itemManager);

            // 创建测试玩家
            HumDataInfo humData;
            humData.charName = "EnvTestPlayer";
            humData.abil.HP = 80;
            humData.abil.MP = 60;

            auto player = userEngine->CreatePlayer(humData);
            userEngine->AddPlayer(player);

            // 测试环境更新
            userEngine->ProcessPlayers();

            std::cout << "✓ 环境更新功能测试通过" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 环境更新功能测试失败: " << e.what() << std::endl;
        }
    }

    static void TestPerformance() {
        std::cout << "\n--- 测试性能 ---" << std::endl;
        
        try {
            auto userEngine = std::make_unique<UserEngine>();
            auto mapManager = std::make_shared<MapManager>();
            auto itemManager = std::make_shared<ItemManager>();
            
            mapManager->Initialize("./maps");
            itemManager->Initialize("./data");
            userEngine->Initialize(mapManager, itemManager);

            // 创建多个测试玩家
            for (int i = 0; i < 100; ++i) {
                HumDataInfo humData;
                humData.charName = "PerfTestPlayer" + std::to_string(i);
                humData.level = 1 + (i % 50);
                humData.currentPos = {100 + (i % 10), 100 + (i / 10)};

                auto player = userEngine->CreatePlayer(humData);
                userEngine->AddPlayer(player);
            }

            // 测试性能
            auto startTime = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < 10; ++i) {
                userEngine->ProcessPlayers();
            }
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            std::cout << "✓ 性能测试通过 - 100个玩家10次处理耗时: " << duration.count() << "ms" << std::endl;
        }
        catch (const std::exception& e) {
            std::cout << "✗ 性能测试失败: " << e.what() << std::endl;
        }
    }
};

int main() {
    try {
        UserEngineEnhancedTest::RunAllTests();
        return 0;
    }
    catch (const std::exception& e) {
        std::cerr << "测试异常: " << e.what() << std::endl;
        return 1;
    }
}
