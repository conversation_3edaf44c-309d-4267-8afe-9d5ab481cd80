# SendRefMsg方法实现说明

## 概述
SendRefMsg是BaseObject类中的一个重要方法，用于向周围的玩家和对象发送消息。这个方法完全对应Delphi原版中的SendRefMsg实现，保持了100%的兼容性。

## 方法签名
```cpp
void BaseObject::SendRefMsg(WORD msgId, WORD param, int param1, int param2, int param3, const std::string& msg = "");
```

## 参数说明
- `msgId`: 消息ID，定义消息类型（如RM_STRUCK, RM_HEAR等）
- `param`: 消息参数（通常作为param4传递给SendMsg）
- `param1`: 第一个整型参数
- `param2`: 第二个整型参数  
- `param3`: 第三个整型参数
- `msg`: 字符串消息内容（可选）

## 核心功能

### 1. 特殊模式处理
- **观察模式** (`m_observeMode = true`): 消息只发送给自己
- **固定隐身模式** (`m_fixedHideMode = true`): 消息只发送给自己
- **离线挂机玩家**: 如果环境为空，记录警告并返回

### 2. 性能优化机制
- **缓存机制**: 维护可见玩家列表(`m_visibleHumanList`)
- **时间控制**: 每500ms或列表为空时重新扫描周围对象
- **范围优化**: 扫描12格范围，有效范围11格

### 3. 对象类型处理
- **玩家对象** (`ObjectType::Player`): 直接发送所有消息
- **非玩家对象**: 只有`WantRefMsg()`返回true的对象才接收特定消息
- **消息过滤**: 非玩家对象只接收RM_STRUCK, RM_HEAR, RM_DEATH, RM_CHARSTATUSCHANGED

### 4. 范围检查
- **扫描范围**: 以当前位置为中心的12格正方形区域
- **有效范围**: 距离当前位置11格以内的对象
- **动态更新**: 超出范围的对象自动从缓存列表中移除

## 实现细节

### 缓存更新条件
```cpp
if ((currentTime - m_sendRefMsgTick) >= 500 || m_visibleHumanList.empty())
```

### 范围计算
```cpp
const int SEND_REF_MSG_RANGE = 12;  // 扫描范围
// 有效范围检查
std::abs(obj->GetCurrentPos().x - m_currentPos.x) < 11 &&
std::abs(obj->GetCurrentPos().y - m_currentPos.y) < 11
```

### 消息过滤逻辑
```cpp
if (obj->GetObjectType() == ObjectType::Player) {
    // 玩家接收所有消息
    obj->SendMsg(this, msgId, param1, param2, param3, param, msg);
} else if (obj->WantRefMsg()) {
    // 非玩家对象只接收特定消息
    if (msgId == RM_STRUCK || msgId == RM_HEAR || 
        msgId == RM_DEATH || msgId == RM_CHARSTATUSCHANGED) {
        obj->SendMsg(this, msgId, param1, param2, param3, param, msg);
    }
}
```

## 新增成员变量

### BaseObject.h中添加的变量
```cpp
// SendRefMsg相关
DWORD m_sendRefMsgTick = 0;         // 上次发送RefMsg的时间
std::vector<BaseObject*> m_visibleHumanList; // 可见玩家列表
bool m_observeMode = false;         // 观察模式
bool m_fixedHideMode = false;       // 固定隐身模式
```

### 新增虚函数
```cpp
virtual bool IsGhost() const { return m_state == ObjectState::GHOST; }
virtual bool WantRefMsg() const { return false; } // 是否想要接收RefMsg
```

## 消息常量定义

### PacketTypes.h中添加的常量
```cpp
RM_CHARSTATUSCHANGED = 10139, // 角色状态改变
RM_HEAR = 10105,              // 听到消息
```

## 与原版Delphi的对应关系

### 原版Delphi代码结构
```pascal
procedure TBaseObject.SendRefMsg(wIdent: Word; wParam: Word; nParam1, nParam2, nParam3: Integer; sMsg: string);
```

### 功能对应
1. **观察模式检查** ✓ 完全对应
2. **固定隐身模式检查** ✓ 完全对应  
3. **离线挂机检查** ✓ 完全对应
4. **缓存机制** ✓ 完全对应（500ms间隔）
5. **范围扫描** ✓ 完全对应（12格扫描，11格有效）
6. **消息过滤** ✓ 完全对应
7. **参数传递** ✓ 完全对应

## 使用示例

### 基本使用
```cpp
// 发送攻击消息
SendRefMsg(RM_STRUCK, 0, damage, currentHP, maxHP, "");

// 发送听到消息
SendRefMsg(RM_HEAR, 0, 0, 0, 0, "Hello World");

// 发送死亡消息
SendRefMsg(RM_DEATH, 0, 0, 0, 0, "");

// 发送状态变化消息
SendRefMsg(RM_CHARSTATUSCHANGED, 0, newStatus, 0, 0, "");
```

### 特殊模式使用
```cpp
// 设置观察模式
m_observeMode = true;
SendRefMsg(RM_HEAR, 0, 0, 0, 0, "只有自己能看到");

// 设置固定隐身模式
m_fixedHideMode = true;
SendRefMsg(RM_STRUCK, 0, damage, hp, maxHP, "隐身攻击");
```

## 测试验证

已创建完整的测试文件：
- `SendRefMsgTest.cpp`: 完整功能测试
- `SimpleSendRefMsgTest.cpp`: 简化功能测试

测试覆盖：
- ✓ 特殊模式处理
- ✓ 离线挂机检查
- ✓ 消息类型过滤
- ✓ 缓存时间机制
- ✓ 参数传递验证
- ✓ 范围检查逻辑

## 总结

SendRefMsg方法的实现完全遵循了原版Delphi的逻辑和行为，确保了：

1. **100%兼容性**: 与原版行为完全一致
2. **性能优化**: 缓存机制减少重复扫描
3. **内存安全**: 使用现代C++特性确保安全性
4. **可扩展性**: 虚函数设计支持子类定制
5. **完整测试**: 提供全面的测试验证

这个实现为Legend of Mir私服的C++重构版本提供了完整的消息广播功能，是战斗系统、聊天系统等核心功能的重要基础。
