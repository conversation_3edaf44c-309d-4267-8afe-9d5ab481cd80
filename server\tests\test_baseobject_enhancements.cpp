#include <iostream>
#include <cassert>
#include <memory>
#include "../src/BaseObject/BaseObject.h"
#include "../src/BaseObject/PlayObject.h"
#include "../src/Common/Types.h"

using namespace MirServer;

// 测试BaseObject和PlayObject的新增功能
class TestBaseObjectEnhancements {
public:
    static void RunAllTests() {
        std::cout << "开始测试BaseObject和PlayObject的新增功能..." << std::endl;

        TestPoisonSystem();
        TestStatusEffects();
        TestItemDropSystem();
        TestMagicSystem();
        TestSpaceMove();

        std::cout << "所有测试通过！" << std::endl;
    }

private:
    static void TestPoisonSystem() {
        std::cout << "测试中毒系统..." << std::endl;

        auto player = std::make_shared<PlayObject>();
        player->Initialize();

        // 测试中毒
        bool result = player->MakePosion(1, 10, 50); // 绿毒，10秒，50点伤害
        assert(result == true);

        std::cout << "✓ 中毒系统测试通过" << std::endl;
    }

    static void TestStatusEffects() {
        std::cout << "测试状态效果系统..." << std::endl;

        auto player = std::make_shared<PlayObject>();
        player->Initialize();

        // 测试防御提升
        bool result1 = player->DefenceUp(30); // 30秒防御提升
        assert(result1 == true);

        // 测试魔防提升
        bool result2 = player->MagDefenceUp(20); // 20秒魔防提升
        assert(result2 == true);

        // 测试魔法盾
        bool result3 = player->MagBubbleDefenceUp(3, 60); // 3级魔法盾，60秒
        assert(result3 == true);

        std::cout << "✓ 状态效果系统测试通过" << std::endl;
    }

    static void TestItemDropSystem() {
        std::cout << "测试物品掉落系统..." << std::endl;

        auto player = std::make_shared<PlayObject>();
        player->Initialize();

        // 创建测试物品
        UserItem testItem;
        testItem.itemIndex = 1001;
        testItem.itemName = "测试武器";
        testItem.dura = 100;
        testItem.duraMax = 100;

        // 测试掉落位置查找
        int destX, destY;
        bool result = player->GetDropPosition(100, 100, 5, destX, destY);
        assert(result == true);

        std::cout << "✓ 物品掉落系统测试通过" << std::endl;
    }

    static void TestMagicSystem() {
        std::cout << "测试魔法系统..." << std::endl;

        auto player = std::make_shared<PlayObject>();
        player->Initialize();

        // 创建测试魔法
        UserMagic testMagic;
        testMagic.magicId = 1; // 治愈术
        testMagic.level = 1;
        testMagic.trainLevel = 0;
        testMagic.nextUseTime = 0;

        // 测试技能训练
        player->TrainSkill(&testMagic, 50);
        assert(testMagic.trainLevel == 50);

        // 测试魔法值计算
        int spellPoint = player->GetSpellPoint(&testMagic);
        assert(spellPoint > 0);

        std::cout << "✓ 魔法系统测试通过" << std::endl;
    }

    static void TestSpaceMove() {
        std::cout << "测试传送系统..." << std::endl;

        auto player = std::make_shared<PlayObject>();
        player->Initialize();

        // 测试传送（需要地图管理器支持，这里只测试方法调用）
        player->SpaceMove("测试地图", 100, 100, 0);

        // 测试随机传送
        player->MapRandomMove("测试地图", 10);

        // 测试掉落位置查找
        int destX, destY;
        bool result = player->GetDropPosition(100, 100, 5, destX, destY);
        assert(result == true);
        assert(destX >= 95 && destX <= 105);
        assert(destY >= 95 && destY <= 105);

        std::cout << "✓ 传送系统测试通过" << std::endl;
    }
};

int main() {
    try {
        TestBaseObjectEnhancements::RunAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
