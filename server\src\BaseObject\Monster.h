#pragma once

#include "BaseObject.h"
#include "../Common/GameData.h"
#include <chrono>
#include <vector>
#include <unordered_map>

namespace MirServer {

// 怪物类型
enum class MonsterType : BYTE {
    NORMAL = 0,         // 普通怪物
    ELITE = 1,          // 精英怪物
    BOSS = 2,           // BOSS
    GUARD = 3,          // 守卫型怪物
    PASSIVE = 4,        // 被动怪物（不主动攻击）
    UNDEAD = 5,         // 不死生物
    ANIMAL = 6,         // 动物类
    DEMON = 7,          // 恶魔类
    HUMANOID = 8        // 人形怪物
};

// 怪物AI状态
enum class MonsterAIState : BYTE {
    IDLE = 0,           // 空闲
    PATROL = 1,         // 巡逻
    CHASE = 2,          // 追击
    ATTACK = 3,         // 攻击
    FLEE = 4,           // 逃跑
    RETURN = 5          // 返回出生点
};



// 怪物基类（对应delphi的TMonsterObject）
class Monster : public BaseObject {
public:
    // 怪物技能结构
    struct MonsterSkill {
        WORD skillId;
        BYTE level;
        DWORD cooldown;
        DWORD lastUseTime;
    };

    Monster();
    virtual ~Monster();

    // 重写基类方法
    virtual ObjectType GetObjectType() const override { return ObjectType::MONSTER; }
    virtual void Initialize() override;
    virtual void Finalize() override;
    virtual void Run() override;
    virtual void Die() override;

    // 怪物属性
    MonsterType GetMonsterType() const { return m_monsterType; }
    void SetMonsterType(MonsterType type) { m_monsterType = type; }

    // 经验值和等级
    DWORD GetExpValue() const { return m_expValue; }
    void SetExpValue(DWORD exp) { m_expValue = exp; }
    BYTE GetMonsterLevel() const { return m_level; }
    void SetMonsterLevel(BYTE level);

    // AI相关
    MonsterAIState GetAIState() const { return m_aiState; }
    void SetAIState(MonsterAIState state) { m_aiState = state; }
    void SetAggroRange(int range) { m_aggroRange = range; }
    int GetAggroRange() const { return m_aggroRange; }
    void SetChaseRange(int range) { m_chaseRange = range; }
    int GetChaseRange() const { return m_chaseRange; }

    // 出生点相关
    void SetSpawnPoint(const Point& pos) { m_spawnPoint = pos; }
    const Point& GetSpawnPoint() const { return m_spawnPoint; }
    void SetRespawnTime(DWORD time) { m_respawnTime = time; }
    DWORD GetRespawnTime() const { return m_respawnTime; }

    // 战斗相关
    virtual bool IsAttackTarget(const BaseObject* target) const override;
    virtual bool IsProperTarget(const BaseObject* target) const override;
    virtual void AttackTarget(BaseObject* target); // 移除override，因为基类没有此方法
    virtual void BeAttacked(BaseObject* attacker, int damage) override;

    // 技能相关
    void AddSkill(WORD skillId, BYTE level = 1);
    bool UseSkill(WORD skillId, BaseObject* target = nullptr);

    // 掉落物品
    void AddDropItem(const DropItem& item);
    void ClearDropItems();
    void GenerateDrops(); // 生成掉落物品

    // 特殊能力
    void SetCanPoison(bool can) { m_canPoison = can; }
    bool CanPoison() const { return m_canPoison; }
    void SetCanParalyze(bool can) { m_canParalyze = can; }
    bool CanParalyze() const { return m_canParalyze; }
    void SetMagicResist(int resist) { m_magicResist = resist; }
    int GetMagicResist() const { return m_magicResist; }

    // 召唤相关
    bool IsSummoned() const { return m_master != nullptr; }
    void SetMaster(BaseObject* master) { m_master = master; }
    BaseObject* GetMaster() const { return m_master; }



    // 属性设置
    void SetAttackSpeed(WORD speed) { m_attackSpeed = speed; }
    void SetAttackRange(int range) { m_attackRange = range; }

protected:
    // AI处理
    virtual void ProcessAI();
    virtual void ProcessIdleState();
    virtual void ProcessPatrolState();
    virtual void ProcessChaseState();
    virtual void ProcessAttackState();
    virtual void ProcessFleeState();
    virtual void ProcessReturnState();

    // 搜索目标
    virtual BaseObject* SearchTarget();
    virtual bool IsValidTarget(BaseObject* target) const;

    // 移动相关
    virtual bool MoveToTarget(BaseObject* target);
    virtual bool MoveToPoint(const Point& target);
    virtual void RandomMove();

    // 攻击处理
    virtual void ProcessAttack();
    virtual int CalculateDamage(BaseObject* target);
    virtual void ApplySpecialEffect(BaseObject* target);

    // 内部状态更新
    virtual void OnStateChanged() override;
    virtual void OnHPChanged() override;

    // 战斗属性（子类需要访问）
    WORD m_attackSpeed = 1000;          // 攻击速度（毫秒）
    DWORD m_lastAttackTime = 0;         // 上次攻击时间
    int m_attackRange = 1;              // 攻击范围
    int m_magicResist = 0;              // 魔法抗性

    // AI相关（子类需要访问）
    BaseObject* m_currentTarget = nullptr; // 当前目标

private:
    // 基本属性
    MonsterType m_monsterType = MonsterType::NORMAL;
    BYTE m_level = 1;
    DWORD m_expValue = 10;

    // AI相关
    MonsterAIState m_aiState = MonsterAIState::IDLE;
    int m_aggroRange = 7;               // 仇恨范围
    int m_chaseRange = 15;              // 追击范围
    DWORD m_lastTargetSearchTime = 0;   // 上次搜索目标时间

    // 出生点
    Point m_spawnPoint{0, 0};           // 出生位置
    DWORD m_respawnTime = 60000;        // 重生时间（毫秒）
    DWORD m_deathTime = 0;              // 死亡时间

    // 特殊能力
    bool m_canPoison = false;           // 是否可以使目标中毒
    bool m_canParalyze = false;         // 是否可以使目标麻痹
    int m_poisonLevel = 1;              // 毒性等级
    int m_paralyzeTime = 3000;          // 麻痹时间

    // 技能列表
    std::vector<MonsterSkill> m_skills;

    // 掉落物品
    std::vector<DropItem> m_dropItems;

    // 召唤相关
    BaseObject* m_master = nullptr;     // 主人（如果是召唤物）

    // 移动相关
    DWORD m_lastMoveTime = 0;           // 上次移动时间
    DWORD m_moveInterval = 1000;        // 移动间隔
    Point m_lastPatrolTarget{0, 0};     // 上次巡逻目标
};

// 精英怪物
class EliteMonster : public Monster {
public:
    EliteMonster();
    virtual ~EliteMonster();

    virtual void Initialize() override;

protected:
    virtual int CalculateDamage(BaseObject* target) override;
};

// BOSS怪物
class BossMonster : public Monster {
public:
    BossMonster();
    virtual ~BossMonster();

    virtual void Initialize() override;
    virtual void Die() override;

    // BOSS特有功能
    void SetImmunities(bool stunImmune, bool poisonImmune, bool paralysisImmune);
    void AddPhaseSkill(int phase, WORD skillId); // 阶段技能

protected:
    virtual void ProcessAI() override;
    virtual void ProcessBossPhase(); // 处理BOSS阶段

private:
    // 免疫状态
    bool m_stunImmune = true;
    bool m_poisonImmune = true;
    bool m_paralysisImmune = true;

    // 阶段相关
    int m_currentPhase = 1;
    std::unordered_map<int, std::vector<WORD>> m_phaseSkills;
};

// 智能指针类型定义
using MonsterPtr = std::shared_ptr<Monster>;
using EliteMonsterPtr = std::shared_ptr<EliteMonster>;
using BossMonsterPtr = std::shared_ptr<BossMonster>;

} // namespace MirServer