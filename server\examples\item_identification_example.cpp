#include "../src/GameEngine/ItemManager.h"
#include "../src/Common/Logger.h"
#include <iostream>
#include <vector>

using namespace MirServer;

// 示例：物品鉴定系统演示
int main() {
    // 初始化日志系统
    Logger::Initialize("example_logs", LogLevel::INFO);
    
    std::cout << "=== 传奇私服物品鉴定系统演示 ===" << std::endl;
    std::cout << std::endl;
    
    // 创建ItemManager实例
    auto itemManager = std::make_unique<ItemManager>();
    
    // 演示1：检查物品是否需要鉴定
    std::cout << "1. 检查物品是否在鉴定名单中：" << std::endl;
    
    std::vector<std::string> testItems = {
        "未知戒指", "未知项链", "未知手镯", "未知武器",
        "普通戒指", "铁剑", "布衣", "传说之剑", "史诗护甲"
    };
    
    for (const auto& itemName : testItems) {
        bool needsIdentify = itemManager->GetGameLogItemNameList(itemName);
        std::cout << "  " << itemName << ": " 
                  << (needsIdentify ? "需要鉴定" : "不需要鉴定") << std::endl;
    }
    
    std::cout << std::endl;
    
    // 演示2：创建物品并检查鉴定状态
    std::cout << "2. 创建物品并检查鉴定状态：" << std::endl;
    
    // 创建需要鉴定的物品
    UserItem mysteriousRing;
    mysteriousRing.itemIndex = 1001;
    mysteriousRing.itemName = "未知戒指";
    mysteriousRing.identified = false; // 未鉴定状态
    mysteriousRing.dura = 1000;
    mysteriousRing.duraMax = 1000;
    mysteriousRing.makeIndex = 12345;
    
    std::cout << "  创建物品: " << mysteriousRing.itemName << std::endl;
    std::cout << "  鉴定状态: " << (mysteriousRing.identified ? "已鉴定" : "未鉴定") << std::endl;
    std::cout << "  显示名称: " << itemManager->GetItemDisplayName(mysteriousRing) << std::endl;
    
    // 执行鉴定
    std::cout << "  执行鉴定..." << std::endl;
    mysteriousRing.identified = true;
    
    std::cout << "  鉴定后状态: " << (mysteriousRing.identified ? "已鉴定" : "未鉴定") << std::endl;
    std::cout << "  显示名称: " << itemManager->GetItemDisplayName(mysteriousRing) << std::endl;
    
    std::cout << std::endl;
    
    // 演示3：不同类型物品的鉴定需求
    std::cout << "3. 不同类型物品的鉴定需求：" << std::endl;
    
    struct ItemExample {
        std::string name;
        bool needsIdentify;
        std::string description;
    };
    
    std::vector<ItemExample> examples = {
        {"未知戒指", true, "神秘的戒指，需要鉴定才能知道属性"},
        {"未知项链", true, "神秘的项链，可能有强大的魔法属性"},
        {"传说之剑", true, "传说级武器，属性未知"},
        {"普通铁剑", false, "普通的铁制武器，属性明确"},
        {"红药水", false, "恢复生命值的药水，无需鉴定"},
        {"金币", false, "游戏货币，无需鉴定"}
    };
    
    for (const auto& example : examples) {
        bool actualNeedsIdentify = itemManager->GetGameLogItemNameList(example.name);
        std::cout << "  " << example.name << ": " 
                  << (actualNeedsIdentify ? "需要鉴定" : "不需要鉴定")
                  << " - " << example.description << std::endl;
    }
    
    std::cout << std::endl;
    
    // 演示4：鉴定系统的游戏逻辑
    std::cout << "4. 鉴定系统的游戏逻辑：" << std::endl;
    std::cout << "  - 未鉴定的物品显示为'未知物品'，玩家无法看到真实属性" << std::endl;
    std::cout << "  - 玩家需要使用鉴定卷轴或找NPC鉴定师进行鉴定" << std::endl;
    std::cout << "  - 鉴定后的物品显示真实名称和属性" << std::endl;
    std::cout << "  - 高级装备通常需要鉴定，增加游戏的神秘感和探索乐趣" << std::endl;
    
    std::cout << std::endl;
    
    // 演示5：鉴定名单统计
    std::cout << "5. 鉴定名单统计：" << std::endl;
    
    int totalChecked = 0;
    int needsIdentifyCount = 0;
    
    std::vector<std::string> allTestItems = {
        "未知戒指", "未知项链", "未知手镯", "未知武器", "未知头盔", 
        "未知衣服", "未知靴子", "未知腰带", "神秘", "魔法", "传说", "史诗",
        "普通戒指", "铁剑", "布衣", "红药水", "蓝药水", "金币", "面包"
    };
    
    for (const auto& item : allTestItems) {
        totalChecked++;
        if (itemManager->GetGameLogItemNameList(item)) {
            needsIdentifyCount++;
        }
    }
    
    std::cout << "  总检查物品数: " << totalChecked << std::endl;
    std::cout << "  需要鉴定物品数: " << needsIdentifyCount << std::endl;
    std::cout << "  不需要鉴定物品数: " << (totalChecked - needsIdentifyCount) << std::endl;
    std::cout << "  鉴定物品比例: " << (static_cast<double>(needsIdentifyCount) / totalChecked * 100) << "%" << std::endl;
    
    std::cout << std::endl;
    
    // 演示6：性能测试
    std::cout << "6. 性能测试：" << std::endl;
    
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // 执行大量鉴定检查
    const int testCount = 100000;
    for (int i = 0; i < testCount; ++i) {
        itemManager->GetGameLogItemNameList("未知戒指");
        itemManager->GetGameLogItemNameList("普通戒指");
    }
    
    auto endTime = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);
    
    std::cout << "  执行 " << (testCount * 2) << " 次鉴定检查" << std::endl;
    std::cout << "  总耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "  平均每次检查: " << (static_cast<double>(duration.count()) / (testCount * 2)) << " 微秒" << std::endl;
    
    std::cout << std::endl;
    std::cout << "=== 演示完成 ===" << std::endl;
    
    // 清理
    itemManager.reset();
    Logger::Finalize();
    
    return 0;
}
