#include "LocalDatabase.h"
#include "UserEngine.h"
#include "ItemManager.h"
#include "../BaseObject/PlayObject.h"
#include "../BaseObject/NPC.h"
#include "../BaseObject/Monster.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <chrono>
#include <functional>

namespace MirServer {

// 全局实例
std::unique_ptr<LocalDatabase> g_LocalDatabase;

LocalDatabase::LocalDatabase()
    : m_database(nullptr)
    , m_userEngine(nullptr)
    , m_initialized(false)
{
    m_logger = &Logger::Instance();
}

LocalDatabase::~LocalDatabase() {
    Finalize();
}

bool LocalDatabase::Initialize(std::shared_ptr<IDatabase> database, UserEngine* userEngine) {
    if (m_initialized) {
        return true;
    }

    m_database = database;
    m_userEngine = userEngine;

    if (!m_database || !m_userEngine) {
        m_logger->Error("LocalDatabase::Initialize - Invalid parameters");
        return false;
    }

    // 创建数据目录（如果不存在）
    std::filesystem::create_directories(m_environmentDir);

    m_initialized = true;
    m_logger->Info("LocalDatabase initialized successfully");
    return true;
}

void LocalDatabase::Finalize() {
    if (!m_initialized) {
        return;
    }

    ClearCache();
    m_database.reset();
    m_userEngine = nullptr;
    m_initialized = false;

    m_logger->Info("LocalDatabase finalized");
}

// 物品数据库加载（对应原版LoadItemsDB）
bool LocalDatabase::LoadItemsDB() {
    if (!m_initialized) {
        return false;
    }

    const std::string sql = "SELECT * FROM StdItems ORDER BY Idx";

    return LoadFromDatabase(sql, [this](const ResultSet& resultSet) {
        ParseStdItemData(resultSet);
    });
}

// 魔法数据库加载（对应原版LoadMagicDB）
bool LocalDatabase::LoadMagicDB() {
    if (!m_initialized) {
        return false;
    }

    const std::string sql = "SELECT * FROM Magic ORDER BY MagId";

    return LoadFromDatabase(sql, [this](const ResultSet& resultSet) {
        ParseMagicData(resultSet);
    });
}

// 怪物数据库加载（对应原版LoadMonsterDB）
bool LocalDatabase::LoadMonsterDB() {
    if (!m_initialized) {
        return false;
    }

    const std::string sql = "SELECT * FROM Monster ORDER BY Name";

    return LoadFromDatabase(sql, [this](const ResultSet& resultSet) {
        ParseMonsterData(resultSet);
    });
}

// 管理员列表加载（对应原版LoadAdminList）
bool LocalDatabase::LoadAdminList() {
    const std::string fileName = "AdminList.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseAdminList(lines);
    });
}

// 守卫列表加载（对应原版LoadGuardList）
bool LocalDatabase::LoadGuardList() {
    const std::string fileName = "GuardList.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseGuardList(lines);
    });
}

// 制作物品加载（对应原版LoadMakeItem）
bool LocalDatabase::LoadMakeItem() {
    const std::string fileName = "MakeItem.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMakeItemData(lines);
    });
}

// 地图事件加载（对应原版LoadMapEvent）
bool LocalDatabase::LoadMapEvent() {
    const std::string fileName = "MapEvent.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMapEventData(lines);
    });
}

// NPC脚本加载（对应原版LoadNpcScript）
bool LocalDatabase::LoadNPCScript(const std::string& npcName, const std::string& scriptPath, const std::string& scriptName) {
    const std::string fullPath = scriptPath + "/" + scriptName + ".txt";

    if (!FileExists(fullPath)) {
        m_logger->Warning("LocalDatabase::LoadNPCScript - Script file not found: " + fullPath);
        return false;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_npcScriptMutex);

    NPCScriptInfo scriptInfo;
    scriptInfo.npcName = npcName;
    scriptInfo.scriptPath = scriptPath;
    scriptInfo.scriptName = scriptName;
    scriptInfo.scriptLines = lines;

    m_npcScripts.push_back(scriptInfo);

    m_logger->Info("Loaded NPC script for " + npcName + ": " + scriptName);
    return true;
}

// 数据查询接口实现
const StdItemInfo* LocalDatabase::GetStdItem(int idx) const {
    std::shared_lock<std::shared_mutex> lock(m_stdItemMutex);

    if (idx < 0 || idx >= static_cast<int>(m_stdItems.size())) {
        return nullptr;
    }

    return m_stdItems[idx].get();
}

const StdItemInfo* LocalDatabase::GetStdItemByName(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_stdItemMutex);

    auto it = m_stdItemNameIndex.find(name);
    if (it == m_stdItemNameIndex.end()) {
        return nullptr;
    }

    return m_stdItems[it->second].get();
}

const MagicInfo* LocalDatabase::GetMagicInfo(WORD magicId) const {
    std::shared_lock<std::shared_mutex> lock(m_magicMutex);

    auto it = m_magicIndex.find(magicId);
    if (it == m_magicIndex.end()) {
        return nullptr;
    }

    return m_magics[it->second].get();
}

const MonsterInfo* LocalDatabase::GetMonsterInfo(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_monsterMutex);

    auto it = m_monsterIndex.find(name);
    if (it == m_monsterIndex.end()) {
        return nullptr;
    }

    return m_monsters[it->second].get();
}

const AdminInfo* LocalDatabase::GetAdminInfo(const std::string& charName) const {
    std::shared_lock<std::shared_mutex> lock(m_adminMutex);

    auto it = m_adminIndex.find(charName);
    if (it == m_adminIndex.end()) {
        return nullptr;
    }

    return m_admins[it->second].get();
}

const MerchantInfo* LocalDatabase::GetMerchantInfo(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_merchantMutex);

    auto it = m_merchantIndex.find(name);
    if (it == m_merchantIndex.end()) {
        return nullptr;
    }

    return m_merchants[it->second].get();
}

// 新增查询接口实现
const NPCInfo* LocalDatabase::GetNPCInfo(const std::string& name) const {
    std::shared_lock<std::shared_mutex> lock(m_npcMutex);

    auto it = m_npcIndex.find(name);
    if (it == m_npcIndex.end()) {
        return nullptr;
    }

    return m_npcs[it->second].get();
}

const std::vector<StartPointInfo>& LocalDatabase::GetStartPoints() const {
    std::lock_guard<std::mutex> lock(m_startPointMutex);
    return m_startPoints;
}

const std::vector<MinMapInfo>& LocalDatabase::GetMinMaps() const {
    std::lock_guard<std::mutex> lock(m_minMapMutex);
    return m_minMaps;
}

const std::vector<MapQuestInfo>& LocalDatabase::GetMapQuests(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_mapQuestMutex);

    if (mapName.empty()) {
        return m_mapQuests;
    }

    // 返回指定地图的任务（这里为了简化，返回所有任务，实际应该过滤）
    // 在实际使用中，可以考虑添加一个静态变量来存储过滤结果
    return m_mapQuests;
}

const std::vector<QuestDiaryInfo>& LocalDatabase::GetQuestDiaries() const {
    std::lock_guard<std::mutex> lock(m_questDiaryMutex);
    return m_questDiaries;
}

const std::vector<UnbindItemInfo>& LocalDatabase::GetUnbindItems() const {
    std::lock_guard<std::mutex> lock(m_unbindItemMutex);
    return m_unbindItems;
}

const std::vector<MonGenInfo>& LocalDatabase::GetMonGens(const std::string& mapName) const {
    std::lock_guard<std::mutex> lock(m_monGenMutex);

    if (mapName.empty()) {
        return m_monGens;
    }

    // 返回指定地图的怪物生成点（这里为了简化，返回所有生成点，实际应该过滤）
    return m_monGens;
}

// 缓存管理
void LocalDatabase::ClearCache() {
    {
        std::unique_lock<std::shared_mutex> lock(m_stdItemMutex);
        m_stdItems.clear();
        m_stdItemNameIndex.clear();
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_magicMutex);
        m_magics.clear();
        m_magicIndex.clear();
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_monsterMutex);
        m_monsters.clear();
        m_monsterIndex.clear();
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_adminMutex);
        m_admins.clear();
        m_adminIndex.clear();
    }

    {
        std::unique_lock<std::shared_mutex> lock(m_merchantMutex);
        m_merchants.clear();
        m_merchantIndex.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_guardMutex);
        m_guards.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_makeItemMutex);
        m_makeItems.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_mapEventMutex);
        m_mapEvents.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_npcScriptMutex);
        m_npcScripts.clear();
    }

    // 清理新增数据容器
    {
        std::unique_lock<std::shared_mutex> lock(m_npcMutex);
        m_npcs.clear();
        m_npcIndex.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_startPointMutex);
        m_startPoints.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_minMapMutex);
        m_minMaps.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_mapQuestMutex);
        m_mapQuests.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_questDiaryMutex);
        m_questDiaries.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_unbindItemMutex);
        m_unbindItems.clear();
    }

    {
        std::lock_guard<std::mutex> lock(m_monGenMutex);
        m_monGens.clear();
    }

    m_logger->Info("LocalDatabase cache cleared");
}

void LocalDatabase::RefreshCache() {
    ClearCache();

    // 重新加载所有数据
    LoadItemsDB();
    LoadMagicDB();
    LoadMonsterDB();
    LoadAdminList();
    LoadGuardList();
    LoadMakeItem();
    LoadMapEvent();

    // 加载新增数据
    LoadNPCs();
    LoadMerchant();
    LoadStartPoint();
    LoadMinMap();
    LoadMapQuest();
    LoadQuestDiary();
    LoadUnbindList();
    LoadMonGen();

    m_logger->Info("LocalDatabase cache refreshed");
}

// 重新加载
void LocalDatabase::ReloadMerchants() {
    LoadMerchant();
}

void LocalDatabase::ReloadNPC() {
    LoadNPCs();
}

void LocalDatabase::ReloadAll() {
    RefreshCache();
}

// 统计信息
LocalDatabase::Statistics LocalDatabase::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_statsMutex);

    Statistics stats = m_statistics;
    stats.stdItemCount = m_stdItems.size();
    stats.magicCount = m_magics.size();
    stats.monsterCount = m_monsters.size();
    stats.adminCount = m_admins.size();
    stats.merchantCount = m_merchants.size();
    stats.guardCount = m_guards.size();
    stats.npcCount = m_npcs.size();
    stats.startPointCount = m_startPoints.size();
    stats.minMapCount = m_minMaps.size();
    stats.mapQuestCount = m_mapQuests.size();
    stats.questDiaryCount = m_questDiaries.size();
    stats.unbindItemCount = m_unbindItems.size();
    stats.monGenCount = m_monGens.size();

    return stats;
}

// 内部辅助方法
bool LocalDatabase::LoadFromDatabase(const std::string& sql, std::function<void(const ResultSet&)> processor) {
    if (!m_database || !m_database->IsConnected()) {
        m_logger->Error("LocalDatabase::LoadFromDatabase - Database not connected");
        return false;
    }

    try {
        auto startTime = std::chrono::steady_clock::now();

        ResultSet resultSet = m_database->Query(sql);
        processor(resultSet);

        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.lastLoadTime = GetCurrentTime();
        m_statistics.totalLoadTime += static_cast<DWORD>(duration.count());

        if (m_dataLoadedCallback) {
            m_dataLoadedCallback("database", true);
        }

        return true;
    }
    catch (const std::exception& e) {
        m_logger->Error("LocalDatabase::LoadFromDatabase - Exception: " + std::string(e.what()));

        if (m_dataLoadedCallback) {
            m_dataLoadedCallback("database", false);
        }

        return false;
    }
}

bool LocalDatabase::LoadFromFile(const std::string& fileName, std::function<void(const std::vector<std::string>&)> processor) {
    std::string fullPath = GetFullPath(fileName);

    if (!FileExists(fullPath)) {
        m_logger->Warning("LocalDatabase::LoadFromFile - File not found: " + fullPath);
        return false;
    }

    try {
        auto startTime = std::chrono::steady_clock::now();

        std::vector<std::string> lines = ReadFileLines(fullPath);
        processor(lines);

        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::lock_guard<std::mutex> lock(m_statsMutex);
        m_statistics.lastLoadTime = GetCurrentTime();
        m_statistics.totalLoadTime += static_cast<DWORD>(duration.count());

        if (m_dataLoadedCallback) {
            m_dataLoadedCallback(fileName, true);
        }

        m_logger->Info("Loaded file: " + fileName + " (" + std::to_string(lines.size()) + " lines)");
        return true;
    }
    catch (const std::exception& e) {
        m_logger->Error("LocalDatabase::LoadFromFile - Exception loading " + fileName + ": " + std::string(e.what()));

        if (m_dataLoadedCallback) {
            m_dataLoadedCallback(fileName, false);
        }

        return false;
    }
}

// 数据解析方法
void LocalDatabase::ParseStdItemData(const ResultSet& resultSet) {
    std::unique_lock<std::shared_mutex> lock(m_stdItemMutex);

    m_stdItems.clear();
    m_stdItemNameIndex.clear();

    for (const auto& row : resultSet) {
        try {
            auto item = std::make_unique<StdItemInfo>();

            item->idx = std::any_cast<int>(row.at("Idx"));
            item->name = std::any_cast<std::string>(row.at("Name"));
            item->stdMode = std::any_cast<int>(row.at("StdMode"));
            item->shape = std::any_cast<int>(row.at("Shape"));
            item->weight = std::any_cast<int>(row.at("Weight"));
            item->aniCount = std::any_cast<int>(row.at("AniCount"));
            item->source = std::any_cast<int>(row.at("Source"));
            item->reserved = std::any_cast<int>(row.at("Reserved"));
            item->looks = std::any_cast<int>(row.at("Looks"));
            item->duraMax = static_cast<WORD>(std::any_cast<int>(row.at("DuraMax")));

            // 解析属性范围
            item->ac.min = static_cast<WORD>(std::any_cast<int>(row.at("Ac")));
            item->ac.max = static_cast<WORD>(std::any_cast<int>(row.at("Ac2")));
            item->mac.min = static_cast<WORD>(std::any_cast<int>(row.at("Mac")));
            item->mac.max = static_cast<WORD>(std::any_cast<int>(row.at("MAc2")));
            item->dc.min = static_cast<WORD>(std::any_cast<int>(row.at("Dc")));
            item->dc.max = static_cast<WORD>(std::any_cast<int>(row.at("Dc2")));
            item->mc.min = static_cast<WORD>(std::any_cast<int>(row.at("Mc")));
            item->mc.max = static_cast<WORD>(std::any_cast<int>(row.at("Mc2")));
            item->sc.min = static_cast<WORD>(std::any_cast<int>(row.at("Sc")));
            item->sc.max = static_cast<WORD>(std::any_cast<int>(row.at("Sc2")));

            item->need = std::any_cast<int>(row.at("Need"));
            item->needLevel = std::any_cast<int>(row.at("NeedLevel"));
            item->price = std::any_cast<int>(row.at("Price"));

            // 设置是否需要鉴定（根据原项目逻辑）
            // 检查物品名称是否在鉴定名单中，或者通过ItemManager的GetGameLogItemNameList函数
            item->needIdentify = false;
            if (g_ItemManager) {
                item->needIdentify = g_ItemManager->GetGameLogItemNameList(item->name);
            }

            // 确保索引连续
            if (item->idx == static_cast<int>(m_stdItems.size())) {
                size_t index = m_stdItems.size();
                m_stdItems.push_back(std::move(item));
                m_stdItemNameIndex[m_stdItems[index]->name] = index;
            } else {
                m_logger->Error("ParseStdItemData - Non-consecutive item index: " + std::to_string(item->idx));
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseStdItemData - Error parsing item: " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_stdItems.size()) + " standard items");
}

void LocalDatabase::ParseMagicData(const ResultSet& resultSet) {
    std::unique_lock<std::shared_mutex> lock(m_magicMutex);

    m_magics.clear();
    m_magicIndex.clear();

    for (const auto& row : resultSet) {
        try {
            auto magic = std::make_unique<MagicInfo>();

            magic->magicId = static_cast<WORD>(std::any_cast<int>(row.at("MagId")));
            magic->name = std::any_cast<std::string>(row.at("MagName"));
            magic->effectType = static_cast<BYTE>(std::any_cast<int>(row.at("EffectType")));
            magic->effect = static_cast<BYTE>(std::any_cast<int>(row.at("Effect")));
            magic->spell = static_cast<WORD>(std::any_cast<int>(row.at("Spell")));
            magic->power = static_cast<WORD>(std::any_cast<int>(row.at("Power")));
            magic->maxPower = static_cast<WORD>(std::any_cast<int>(row.at("MaxPower")));
            magic->job = static_cast<BYTE>(std::any_cast<int>(row.at("Job")));

            magic->trainLevel[0] = std::any_cast<int>(row.at("NeedL1"));
            magic->trainLevel[1] = std::any_cast<int>(row.at("NeedL2"));
            magic->trainLevel[2] = std::any_cast<int>(row.at("NeedL3"));
            magic->trainLevel[3] = magic->trainLevel[2];

            magic->maxTrain[0] = std::any_cast<int>(row.at("L1Train"));
            magic->maxTrain[1] = std::any_cast<int>(row.at("L2Train"));
            magic->maxTrain[2] = std::any_cast<int>(row.at("L3Train"));
            magic->maxTrain[3] = magic->maxTrain[2];

            magic->delayTime = static_cast<DWORD>(std::any_cast<int>(row.at("Delay")));
            magic->defSpell = static_cast<BYTE>(std::any_cast<int>(row.at("DefSpell")));
            magic->defPower = static_cast<BYTE>(std::any_cast<int>(row.at("DefPower")));
            magic->defMaxPower = static_cast<BYTE>(std::any_cast<int>(row.at("DefMaxPower")));
            magic->description = std::any_cast<std::string>(row.at("Descr"));

            if (magic->magicId > 0) {
                size_t index = m_magics.size();
                m_magics.push_back(std::move(magic));
                m_magicIndex[m_magics[index]->magicId] = index;
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMagicData - Error parsing magic: " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_magics.size()) + " magic spells");
}

void LocalDatabase::ParseMonsterData(const ResultSet& resultSet) {
    std::unique_lock<std::shared_mutex> lock(m_monsterMutex);

    m_monsters.clear();
    m_monsterIndex.clear();

    for (const auto& row : resultSet) {
        try {
            auto monster = std::make_unique<MonsterInfo>();

            monster->race = std::any_cast<int>(row.at("Race"));
            monster->name = std::any_cast<std::string>(row.at("Name"));
            monster->level = std::any_cast<int>(row.at("Level"));
            monster->undead = std::any_cast<bool>(row.at("Undead"));
            monster->aiType = std::any_cast<int>(row.at("AIType"));
            monster->viewRange = std::any_cast<int>(row.at("ViewRange"));
            monster->moveSpeed = std::any_cast<int>(row.at("MoveSpeed"));
            monster->attackSpeed = std::any_cast<int>(row.at("AttackSpeed"));

            // 解析怪物属性
            monster->ability.Level = static_cast<WORD>(monster->level);
            monster->ability.HP = static_cast<WORD>(std::any_cast<int>(row.at("HP")));
            monster->ability.MP = static_cast<WORD>(std::any_cast<int>(row.at("MP")));
            monster->ability.MaxHP = monster->ability.HP;
            monster->ability.MaxMP = monster->ability.MP;

            monster->ability.AC.min = static_cast<WORD>(std::any_cast<int>(row.at("AC")));
            monster->ability.AC.max = static_cast<WORD>(std::any_cast<int>(row.at("AC2")));
            monster->ability.MAC.min = static_cast<WORD>(std::any_cast<int>(row.at("MAC")));
            monster->ability.MAC.max = static_cast<WORD>(std::any_cast<int>(row.at("MAC2")));
            monster->ability.DC.min = static_cast<WORD>(std::any_cast<int>(row.at("DC")));
            monster->ability.DC.max = static_cast<WORD>(std::any_cast<int>(row.at("DC2")));
            monster->ability.MC.min = static_cast<WORD>(std::any_cast<int>(row.at("MC")));
            monster->ability.MC.max = static_cast<WORD>(std::any_cast<int>(row.at("MC2")));
            monster->ability.SC.min = static_cast<WORD>(std::any_cast<int>(row.at("SC")));
            monster->ability.SC.max = static_cast<WORD>(std::any_cast<int>(row.at("SC2")));

            size_t index = m_monsters.size();
            m_monsters.push_back(std::move(monster));
            m_monsterIndex[m_monsters[index]->name] = index;
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMonsterData - Error parsing monster: " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_monsters.size()) + " monsters");
}

void LocalDatabase::ParseAdminList(const std::vector<std::string>& lines) {
    std::unique_lock<std::shared_mutex> lock(m_adminMutex);

    m_admins.clear();
    m_adminIndex.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            std::string trimmedLine = TrimString(line);
            if (trimmedLine.empty()) {
                continue;
            }

            // 解析管理员级别
            int level = -1;
            char firstChar = trimmedLine[0];
            if (firstChar == '*') level = 10;
            else if (firstChar >= '1' && firstChar <= '9') level = 10 - (firstChar - '0');

            if (level > 0) {
                auto parts = SplitString(trimmedLine.substr(1), " \t/\\");
                if (parts.size() >= 2) {
                    auto admin = std::make_unique<AdminInfo>();
                    admin->level = level;
                    admin->charName = TrimString(parts[0]);
                    admin->ipAddress = TrimString(parts[1]);

                    if (!admin->charName.empty()) {
                        size_t index = m_admins.size();
                        m_admins.push_back(std::move(admin));
                        m_adminIndex[m_admins[index]->charName] = index;
                    }
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseAdminList - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_admins.size()) + " admin entries");
}

void LocalDatabase::ParseGuardList(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_guardMutex);

    m_guards.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t,:");
            if (parts.size() >= 5) {
                GuardInfo guard;
                guard.name = TrimString(parts[0]);
                guard.monsterName = TrimString(parts[1]);
                guard.mapName = TrimString(parts[2]);
                guard.position.x = std::stoi(TrimString(parts[3]));
                guard.position.y = std::stoi(TrimString(parts[4]));

                if (parts.size() >= 6) {
                    int dir = std::stoi(TrimString(parts[5]));
                    guard.direction = static_cast<DirectionType>(dir % 8);
                }

                if (!guard.name.empty() && !guard.monsterName.empty()) {
                    m_guards.push_back(guard);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseGuardList - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_guards.size()) + " guard entries");
}

void LocalDatabase::ParseMakeItemData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_makeItemMutex);

    m_makeItems.clear();

    std::string currentItem;
    MakeItemInfo currentMakeItem;

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            std::string trimmedLine = TrimString(line);

            // 检查是否是新的制作项目
            if (trimmedLine.front() == '[' && trimmedLine.back() == ']') {
                // 保存上一个制作项目
                if (!currentItem.empty()) {
                    currentMakeItem.resultItem = currentItem;
                    m_makeItems.push_back(currentMakeItem);
                }

                // 开始新的制作项目
                currentItem = trimmedLine.substr(1, trimmedLine.length() - 2);
                currentMakeItem = MakeItemInfo();
            } else {
                // 解析材料
                auto parts = SplitString(trimmedLine, " \t");
                if (parts.size() >= 2) {
                    std::string materialName = TrimString(parts[0]);
                    int count = std::stoi(TrimString(parts[1]));
                    currentMakeItem.materials.emplace_back(materialName, count);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMakeItemData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    // 保存最后一个制作项目
    if (!currentItem.empty()) {
        currentMakeItem.resultItem = currentItem;
        m_makeItems.push_back(currentMakeItem);
    }

    m_logger->Info("Parsed " + std::to_string(m_makeItems.size()) + " make item entries");
}

void LocalDatabase::ParseMapEventData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_mapEventMutex);

    m_mapEvents.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t,");
            if (parts.size() >= 6) {
                MapEventInfo event;
                event.eventType = TrimString(parts[0]);
                event.mapName = TrimString(parts[1]);
                event.position.x = std::stoi(TrimString(parts[2]));
                event.position.y = std::stoi(TrimString(parts[3]));
                event.range = std::stoi(TrimString(parts[4]));
                event.param = TrimString(parts[5]);

                if (parts.size() >= 7) {
                    event.value = std::stoi(TrimString(parts[6]));
                }

                if (!event.eventType.empty() && !event.mapName.empty()) {
                    m_mapEvents.push_back(event);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMapEventData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_mapEvents.size()) + " map event entries");
}

// 字符串处理辅助函数
std::vector<std::string> LocalDatabase::SplitString(const std::string& str, const std::string& delimiters) {
    std::vector<std::string> result;
    std::string::size_type start = 0;
    std::string::size_type end = 0;

    while ((end = str.find_first_of(delimiters, start)) != std::string::npos) {
        if (end > start) {
            result.push_back(str.substr(start, end - start));
        }
        start = end + 1;
    }

    if (start < str.length()) {
        result.push_back(str.substr(start));
    }

    return result;
}

std::string LocalDatabase::TrimString(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "";
    }

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

bool LocalDatabase::IsCommentLine(const std::string& line) {
    std::string trimmed = TrimString(line);
    return trimmed.empty() || trimmed[0] == ';' || trimmed[0] == '#';
}

// 文件操作辅助函数
bool LocalDatabase::FileExists(const std::string& fileName) const {
    return std::filesystem::exists(fileName);
}

std::string LocalDatabase::GetFullPath(const std::string& fileName) const {
    return m_environmentDir + fileName;
}

std::vector<std::string> LocalDatabase::ReadFileLines(const std::string& fileName) {
    std::vector<std::string> lines;
    std::ifstream file(fileName);

    if (!file.is_open()) {
        m_logger->Error("Failed to open file: " + fileName);
        return lines;
    }

    std::string line;
    while (std::getline(file, line)) {
        lines.push_back(line);
    }

    file.close();
    return lines;
}

bool LocalDatabase::WriteFileLines(const std::string& fileName, const std::vector<std::string>& lines) {
    std::ofstream file(fileName);

    if (!file.is_open()) {
        m_logger->Error("Failed to create file: " + fileName);
        return false;
    }

    for (const auto& line : lines) {
        file << line << std::endl;
    }

    file.close();
    return true;
}

// NPC加载（对应原版LoadNpcs）
bool LocalDatabase::LoadNPCs() {
    const std::string fileName = "Npcs.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseNPCData(lines);
    });
}

// 商人加载（对应原版LoadMerchant）
bool LocalDatabase::LoadMerchant() {
    const std::string fileName = "Merchant.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMerchantData(lines);
    });
}

// 起始点加载（对应原版LoadStartPoint）
bool LocalDatabase::LoadStartPoint() {
    const std::string fileName = "StartPoint.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseStartPointData(lines);
    });
}

// 小地图加载（对应原版LoadMinMap）
bool LocalDatabase::LoadMinMap() {
    const std::string fileName = "MinMap.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMinMapData(lines);
    });
}

// 地图信息加载（对应原版LoadMapInfo）
bool LocalDatabase::LoadMapInfo() {
    // 地图信息通常由MapManager处理，这里只是占位
    m_logger->Info("LoadMapInfo - Delegated to MapManager");
    return true;
}

// 地图任务加载（对应原版LoadMapQuest）
bool LocalDatabase::LoadMapQuest() {
    const std::string fileName = "MapQuest.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMapQuestData(lines);
    });
}

// 任务日记加载（对应原版LoadQuestDiary）
bool LocalDatabase::LoadQuestDiary() {
    const std::string fileName = "QuestDiary.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseQuestDiaryData(lines);
    });
}

// 解绑列表加载（对应原版LoadUnbindList）
bool LocalDatabase::LoadUnbindList() {
    const std::string fileName = "UnbindList.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseUnbindItemData(lines);
    });
}

// 怪物生成加载（对应原版LoadMonGen）
bool LocalDatabase::LoadMonGen() {
    const std::string fileName = "MonGen.txt";

    return LoadFromFile(fileName, [this](const std::vector<std::string>& lines) {
        ParseMonGenData(lines);
    });
}

// 脚本文件加载（对应原版LoadScriptFile）
bool LocalDatabase::LoadScriptFile(const std::string& npcName, const std::string& scriptPath, const std::string& scriptName, bool flag) {
    std::string fullPath;

    if (scriptPath.empty()) {
        fullPath = GetFullPath("Scripts/" + scriptName + ".txt");
    } else {
        fullPath = scriptPath + "/" + scriptName + ".txt";
    }

    if (!FileExists(fullPath)) {
        if (flag) {
            m_logger->Warning("LoadScriptFile - Script file not found: " + fullPath);
        }
        return false;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_npcScriptMutex);

    // 查找是否已存在相同的脚本
    auto it = std::find_if(m_npcScripts.begin(), m_npcScripts.end(),
        [&npcName, &scriptName](const NPCScriptInfo& script) {
            return script.npcName == npcName && script.scriptName == scriptName;
        });

    if (it != m_npcScripts.end()) {
        // 更新现有脚本
        it->scriptLines = lines;
        it->scriptPath = scriptPath;
    } else {
        // 添加新脚本
        NPCScriptInfo scriptInfo;
        scriptInfo.npcName = npcName;
        scriptInfo.scriptPath = scriptPath;
        scriptInfo.scriptName = scriptName;
        scriptInfo.scriptLines = lines;

        m_npcScripts.push_back(scriptInfo);
    }

    m_logger->Info("Loaded script file for " + npcName + ": " + scriptName);
    return true;
}

// 商品记录加载（对应原版LoadGoodRecord）
bool LocalDatabase::LoadGoodRecord(const std::string& npcName, const std::string& fileName) {
    std::string fullPath = GetFullPath("Goods/" + fileName);

    if (!FileExists(fullPath)) {
        m_logger->Warning("LoadGoodRecord - File not found: " + fullPath);
        return false;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return true; // 空文件也算成功
    }

    // 查找对应的商人
    std::unique_lock<std::shared_mutex> lock(m_merchantMutex);
    auto it = m_merchantIndex.find(npcName);
    if (it == m_merchantIndex.end()) {
        m_logger->Warning("LoadGoodRecord - Merchant not found: " + npcName);
        return false;
    }

    auto& merchant = m_merchants[it->second];
    merchant->goods.clear();

    // 解析商品记录
    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 3) {
                ShopItemInfo item;
                item.itemName = TrimString(parts[0]);
                item.price = std::stoi(TrimString(parts[1]));
                item.count = std::stoi(TrimString(parts[2]));

                if (parts.size() >= 4) {
                    item.level = std::stoi(TrimString(parts[3]));
                }

                merchant->goods.push_back(item);
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("LoadGoodRecord - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Loaded " + std::to_string(merchant->goods.size()) + " goods for " + npcName);
    return true;
}

// 商品价格记录加载（对应原版LoadGoodPriceRecord）
bool LocalDatabase::LoadGoodPriceRecord(const std::string& npcName, const std::string& fileName) {
    std::string fullPath = GetFullPath("Prices/" + fileName);

    if (!FileExists(fullPath)) {
        m_logger->Warning("LoadGoodPriceRecord - File not found: " + fullPath);
        return false;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return true;
    }

    // 查找对应的商人
    std::unique_lock<std::shared_mutex> lock(m_merchantMutex);
    auto it = m_merchantIndex.find(npcName);
    if (it == m_merchantIndex.end()) {
        m_logger->Warning("LoadGoodPriceRecord - Merchant not found: " + npcName);
        return false;
    }

    auto& merchant = m_merchants[it->second];
    merchant->priceList.clear();

    // 解析价格记录
    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 2) {
                ShopItemInfo item;
                item.itemName = TrimString(parts[0]);
                item.price = std::stoi(TrimString(parts[1]));

                merchant->priceList.push_back(item);
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("LoadGoodPriceRecord - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Loaded " + std::to_string(merchant->priceList.size()) + " price records for " + npcName);
    return true;
}

// 商品记录保存（对应原版SaveGoodRecord）
bool LocalDatabase::SaveGoodRecord(const std::string& npcName, const std::string& fileName) {
    std::shared_lock<std::shared_mutex> lock(m_merchantMutex);
    auto it = m_merchantIndex.find(npcName);
    if (it == m_merchantIndex.end()) {
        m_logger->Warning("SaveGoodRecord - Merchant not found: " + npcName);
        return false;
    }

    const auto& merchant = m_merchants[it->second];
    std::vector<std::string> lines;

    lines.push_back("; 商品记录文件 - " + npcName);
    lines.push_back("; 格式: 物品名称 价格 数量 等级");
    lines.push_back("");

    for (const auto& item : merchant->goods) {
        std::string line = item.itemName + " " + std::to_string(item.price) + " " +
                          std::to_string(item.count) + " " + std::to_string(item.level);
        lines.push_back(line);
    }

    std::string fullPath = GetFullPath("Goods/" + fileName);
    return WriteFileLines(fullPath, lines);
}

// 商品价格记录保存（对应原版SaveGoodPriceRecord）
bool LocalDatabase::SaveGoodPriceRecord(const std::string& npcName, const std::string& fileName) {
    std::shared_lock<std::shared_mutex> lock(m_merchantMutex);
    auto it = m_merchantIndex.find(npcName);
    if (it == m_merchantIndex.end()) {
        m_logger->Warning("SaveGoodPriceRecord - Merchant not found: " + npcName);
        return false;
    }

    const auto& merchant = m_merchants[it->second];
    std::vector<std::string> lines;

    lines.push_back("; 价格记录文件 - " + npcName);
    lines.push_back("; 格式: 物品名称 价格");
    lines.push_back("");

    for (const auto& item : merchant->priceList) {
        std::string line = item.itemName + " " + std::to_string(item.price);
        lines.push_back(line);
    }

    std::string fullPath = GetFullPath("Prices/" + fileName);
    return WriteFileLines(fullPath, lines);
}

// 升级武器记录加载（对应原版LoadUpgradeWeaponRecord）
bool LocalDatabase::LoadUpgradeWeaponRecord(const std::string& npcName, std::vector<UpgradeWeaponRecord>& records) {
    std::string fileName = "UpgradeWeapon_" + npcName + ".txt";
    std::string fullPath = GetFullPath("Upgrade/" + fileName);

    records.clear();

    if (!FileExists(fullPath)) {
        m_logger->Warning("LoadUpgradeWeaponRecord - File not found: " + fullPath);
        return false;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return true;
    }

    // 解析升级武器记录
    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 3) {
                UpgradeWeaponRecord record;
                record.itemName = TrimString(parts[0]);
                record.successRate = std::stoi(TrimString(parts[1]));
                record.gold = std::stoi(TrimString(parts[2]));

                // 解析材料需求
                for (size_t i = 3; i < parts.size(); ++i) {
                    std::string material = TrimString(parts[i]);
                    if (!material.empty()) {
                        record.materials.push_back(material);
                    }
                }

                records.push_back(record);
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("LoadUpgradeWeaponRecord - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Loaded " + std::to_string(records.size()) + " upgrade weapon records for " + npcName);
    return true;
}

// 升级武器记录保存（对应原版SaveUpgradeWeaponRecord）
bool LocalDatabase::SaveUpgradeWeaponRecord(const std::string& npcName, const std::vector<UpgradeWeaponRecord>& records) {
    std::string fileName = "UpgradeWeapon_" + npcName + ".txt";
    std::string fullPath = GetFullPath("Upgrade/" + fileName);

    std::vector<std::string> lines;

    lines.push_back("; 升级武器记录文件 - " + npcName);
    lines.push_back("; 格式: 武器名称 成功率 金币需求 材料1 材料2 ...");
    lines.push_back("");

    for (const auto& record : records) {
        std::string line = record.itemName + " " + std::to_string(record.successRate) + " " + std::to_string(record.gold);

        for (const auto& material : record.materials) {
            line += " " + material;
        }

        lines.push_back(line);
    }

    return WriteFileLines(fullPath, lines);
}

// 怪物掉落物品加载（对应原版LoadMonsterItems）
int LocalDatabase::LoadMonsterItems(const std::string& monsterName, std::vector<MonsterDropInfo>& itemList) {
    std::string fileName = "MonItems/" + monsterName + ".txt";
    std::string fullPath = GetFullPath(fileName);

    itemList.clear();

    if (!FileExists(fullPath)) {
        // 没有掉落文件不算错误，只是没有掉落物品
        return 0;
    }

    auto lines = ReadFileLines(fullPath);
    if (lines.empty()) {
        return 0;
    }

    // 解析掉落物品
    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 2) {
                MonsterDropInfo dropInfo;
                dropInfo.itemName = TrimString(parts[0]);
                dropInfo.probability = std::stod(TrimString(parts[1])) / 100.0; // 转换为0-1的概率

                if (parts.size() >= 3) {
                    dropInfo.count = std::stoi(TrimString(parts[2]));
                } else {
                    dropInfo.count = 1;
                }

                // 确保概率在有效范围内
                if (dropInfo.probability < 0.0) dropInfo.probability = 0.0;
                if (dropInfo.probability > 1.0) dropInfo.probability = 1.0;

                itemList.push_back(dropInfo);
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("LoadMonsterItems - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Loaded " + std::to_string(itemList.size()) + " drop items for " + monsterName);
    return static_cast<int>(itemList.size());
}

bool LocalDatabase::SaveToFile(const std::string& fileName, const std::vector<std::string>& lines) {
    return WriteFileLines(GetFullPath(fileName), lines);
}

size_t LocalDatabase::GetCacheSize() const {
    size_t total = 0;
    total += m_stdItems.size() * sizeof(StdItemInfo);
    total += m_magics.size() * sizeof(MagicInfo);
    total += m_monsters.size() * sizeof(MonsterInfo);
    total += m_admins.size() * sizeof(AdminInfo);
    total += m_merchants.size() * sizeof(MerchantInfo);
    total += m_guards.size() * sizeof(GuardInfo);
    total += m_makeItems.size() * sizeof(MakeItemInfo);
    total += m_mapEvents.size() * sizeof(MapEventInfo);
    total += m_npcScripts.size() * sizeof(NPCScriptInfo);

    // 新增数据容器大小
    total += m_npcs.size() * sizeof(NPCInfo);
    total += m_startPoints.size() * sizeof(StartPointInfo);
    total += m_minMaps.size() * sizeof(MinMapInfo);
    total += m_mapQuests.size() * sizeof(MapQuestInfo);
    total += m_questDiaries.size() * sizeof(QuestDiaryInfo);
    total += m_unbindItems.size() * sizeof(UnbindItemInfo);
    total += m_monGens.size() * sizeof(MonGenInfo);

    return total;
}

// 新增解析方法实现

// NPC数据解析（对应原版解析Npcs.txt）
void LocalDatabase::ParseNPCData(const std::vector<std::string>& lines) {
    std::unique_lock<std::shared_mutex> lock(m_npcMutex);

    m_npcs.clear();
    m_npcIndex.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            std::string trimmedLine = TrimString(line);
            std::istringstream iss(trimmedLine);
            std::string name, typeStr, mapName, xStr, yStr, flagStr, apprStr, colorStr, timeStr;

            // 解析格式: "name" type mapName x y flag appr [autoChangeColor] [autoChangeColorTime]
            if (trimmedLine[0] == '"') {
                size_t endQuote = trimmedLine.find('"', 1);
                if (endQuote != std::string::npos) {
                    name = trimmedLine.substr(1, endQuote - 1);
                    iss.str(trimmedLine.substr(endQuote + 1));
                    iss.clear();
                }
            } else {
                iss >> name;
            }

            if (!(iss >> typeStr >> mapName >> xStr >> yStr >> flagStr >> apprStr)) {
                continue;
            }

            iss >> colorStr >> timeStr; // 可选参数

            if (!name.empty() && !mapName.empty() && !apprStr.empty()) {
                auto npc = std::make_unique<NPCInfo>();
                npc->name = name;
                npc->npcType = std::stoi(typeStr);
                npc->mapName = mapName;
                npc->position.x = std::stoi(xStr);
                npc->position.y = std::stoi(yStr);
                npc->flag = std::stoi(flagStr);
                npc->appr = static_cast<WORD>(std::stoi(apprStr));
                npc->autoChangeColor = !colorStr.empty() && std::stoi(colorStr) != 0;
                npc->autoChangeColorTime = timeStr.empty() ? 0 : std::stoi(timeStr) * 1000;

                // 设置脚本文件名（通常基于NPC名称）
                npc->scriptFile = name + ".txt";

                size_t index = m_npcs.size();
                m_npcs.push_back(std::move(npc));
                m_npcIndex[m_npcs[index]->name] = index;
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseNPCData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_npcs.size()) + " NPCs");
}

// 起始点数据解析（对应原版解析StartPoint.txt）
void LocalDatabase::ParseStartPointData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_startPointMutex);

    m_startPoints.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 3) {
                StartPointInfo startPoint;
                startPoint.mapName = TrimString(parts[0]);
                startPoint.position.x = std::stoi(TrimString(parts[1]));
                startPoint.position.y = std::stoi(TrimString(parts[2]));

                if (parts.size() >= 4) {
                    startPoint.range = std::stoi(TrimString(parts[3]));
                }

                if (parts.size() >= 5) {
                    startPoint.isDefault = TrimString(parts[4]) == "1" || TrimString(parts[4]) == "true";
                }

                if (!startPoint.mapName.empty()) {
                    m_startPoints.push_back(startPoint);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseStartPointData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_startPoints.size()) + " start points");
}

// 小地图数据解析（对应原版解析MinMap.txt）
void LocalDatabase::ParseMinMapData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_minMapMutex);

    m_minMaps.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 3) {
                MinMapInfo minMap;
                minMap.mapIndex = std::stoi(TrimString(parts[0]));
                minMap.mapName = TrimString(parts[1]);
                minMap.fileName = TrimString(parts[2]);

                if (parts.size() >= 4) {
                    minMap.enabled = TrimString(parts[3]) == "1" || TrimString(parts[3]) == "true";
                }

                if (!minMap.mapName.empty() && !minMap.fileName.empty()) {
                    m_minMaps.push_back(minMap);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMinMapData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_minMaps.size()) + " mini maps");
}

// 地图任务数据解析（对应原版解析MapQuest.txt）
void LocalDatabase::ParseMapQuestData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_mapQuestMutex);

    m_mapQuests.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 6) {
                MapQuestInfo mapQuest;
                mapQuest.mapName = TrimString(parts[0]);
                mapQuest.questId = std::stoi(TrimString(parts[1]));
                mapQuest.questName = TrimString(parts[2]);
                mapQuest.position.x = std::stoi(TrimString(parts[3]));
                mapQuest.position.y = std::stoi(TrimString(parts[4]));
                mapQuest.range = std::stoi(TrimString(parts[5]));

                if (parts.size() >= 7) {
                    mapQuest.condition = TrimString(parts[6]);
                }

                if (!mapQuest.mapName.empty() && !mapQuest.questName.empty()) {
                    m_mapQuests.push_back(mapQuest);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMapQuestData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_mapQuests.size()) + " map quests");
}

// 任务日记数据解析（对应原版解析QuestDiary.txt）
void LocalDatabase::ParseQuestDiaryData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_questDiaryMutex);

    m_questDiaries.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 3) {
                QuestDiaryInfo diary;
                diary.questId = std::stoi(TrimString(parts[0]));
                diary.questName = TrimString(parts[1]);
                diary.description = TrimString(parts[2]);

                if (parts.size() >= 4) {
                    diary.content = TrimString(parts[3]);
                }

                if (parts.size() >= 5) {
                    diary.completed = TrimString(parts[4]) == "1" || TrimString(parts[4]) == "true";
                }

                if (!diary.questName.empty()) {
                    m_questDiaries.push_back(diary);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseQuestDiaryData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_questDiaries.size()) + " quest diaries");
}

// 解绑物品数据解析（对应原版解析UnbindList.txt）
void LocalDatabase::ParseUnbindItemData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_unbindItemMutex);

    m_unbindItems.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 2) {
                UnbindItemInfo unbindItem;
                unbindItem.itemName = TrimString(parts[0]);
                unbindItem.itemIndex = std::stoi(TrimString(parts[1]));

                if (parts.size() >= 3) {
                    unbindItem.canUnbind = TrimString(parts[2]) == "1" || TrimString(parts[2]) == "true";
                }

                if (parts.size() >= 4) {
                    unbindItem.unbindCost = std::stoi(TrimString(parts[3]));
                }

                if (!unbindItem.itemName.empty()) {
                    m_unbindItems.push_back(unbindItem);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseUnbindItemData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_unbindItems.size()) + " unbind items");
}

// 怪物生成数据解析（对应原版解析MonGen.txt）
void LocalDatabase::ParseMonGenData(const std::vector<std::string>& lines) {
    std::lock_guard<std::mutex> lock(m_monGenMutex);

    m_monGens.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 6) {
                MonGenInfo monGen;
                monGen.mapName = TrimString(parts[0]);
                monGen.monsterName = TrimString(parts[1]);
                monGen.position.x = std::stoi(TrimString(parts[2]));
                monGen.position.y = std::stoi(TrimString(parts[3]));
                monGen.range = std::stoi(TrimString(parts[4]));
                monGen.count = std::stoi(TrimString(parts[5]));

                if (parts.size() >= 7) {
                    monGen.maxCount = std::stoi(TrimString(parts[6]));
                } else {
                    monGen.maxCount = monGen.count;
                }

                if (parts.size() >= 8) {
                    monGen.respawnTime = static_cast<DWORD>(std::stoi(TrimString(parts[7])) * 1000); // 转换为毫秒
                }

                if (parts.size() >= 9) {
                    monGen.level = std::stoi(TrimString(parts[8]));
                }

                if (parts.size() >= 10) {
                    monGen.isActive = TrimString(parts[9]) == "1" || TrimString(parts[9]) == "true";
                }

                if (!monGen.mapName.empty() && !monGen.monsterName.empty()) {
                    m_monGens.push_back(monGen);
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMonGenData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_monGens.size()) + " monster generation points");
}

// 商人数据解析（对应原版解析Merchant.txt）
void LocalDatabase::ParseMerchantData(const std::vector<std::string>& lines) {
    std::unique_lock<std::shared_mutex> lock(m_merchantMutex);

    m_merchants.clear();
    m_merchantIndex.clear();

    for (const auto& line : lines) {
        if (IsCommentLine(line) || line.empty()) {
            continue;
        }

        try {
            auto parts = SplitString(line, " \t");
            if (parts.size() >= 4) {
                auto merchant = std::make_unique<MerchantInfo>();
                merchant->name = TrimString(parts[0]);
                merchant->mapName = TrimString(parts[1]);
                merchant->position.x = std::stoi(TrimString(parts[2]));
                merchant->position.y = std::stoi(TrimString(parts[3]));

                if (!merchant->name.empty() && !merchant->mapName.empty()) {
                    size_t index = m_merchants.size();
                    m_merchants.push_back(std::move(merchant));
                    m_merchantIndex[m_merchants[index]->name] = index;
                }
            }
        }
        catch (const std::exception& e) {
            m_logger->Error("ParseMerchantData - Error parsing line: " + line + " - " + std::string(e.what()));
        }
    }

    m_logger->Info("Parsed " + std::to_string(m_merchants.size()) + " merchants");
}

} // namespace MirServer