// UserEngineTest.cpp - UserEngine完善功能测试
#include "UserEngine.h"
#include "MapManager.h"
#include "ItemManager.h"
#include "MagicManager.h"
#include "StorageManager.h"
#include "TradeManager.h"
#include "QuestManager.h"
#include "MiniMapManager.h"
#include "RepairManager.h"
#include "PKManager.h"
#include "GroupManager.h"
#include "GuildManager.h"
#include "../BaseObject/PlayObject.h"
#include "../Common/Logger.h"
#include <iostream>
#include <memory>
#include <cassert>

using namespace MirServer;

class UserEngineTest {
public:
    static void RunAllTests() {
        std::cout << "=== UserEngine完善功能测试 ===" << std::endl;

        TestInitialization();
        TestPlayerManagement();
        TestEventHandlers();
        TestGMCommands();
        TestPlayerProcessing();
        TestStatistics();

        std::cout << "=== 所有测试通过 ===" << std::endl;
    }

private:
    static void TestInitialization() {
        std::cout << "测试UserEngine初始化..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();
        auto magicManager = std::make_shared<MagicManager>();
        auto storageManager = std::make_shared<StorageManager>();
        auto tradeManager = std::make_shared<TradeManager>();
        auto questManager = std::make_shared<QuestManager>();
        auto miniMapManager = std::make_shared<MiniMapManager>();
        auto repairManager = std::make_shared<RepairManager>();

        // 初始化管理器
        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        magicManager->Initialize();
        storageManager->Initialize();
        tradeManager->Initialize();
        questManager->Initialize();
        miniMapManager->Initialize();
        repairManager->Initialize();

        // 测试UserEngine初始化
        bool result = userEngine->Initialize(mapManager, itemManager, magicManager,
                                           storageManager, tradeManager, questManager,
                                           miniMapManager, repairManager);
        assert(result == true);

        std::cout << "✓ UserEngine初始化测试通过" << std::endl;
    }

    static void TestPlayerManagement() {
        std::cout << "测试玩家管理功能..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        userEngine->Initialize(mapManager, itemManager);

        // 创建测试玩家
        HumDataInfo humData;
        humData.charName = "TestPlayer";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;
        humData.currentPos = Point{100, 100};
        humData.mapName = "0";
        humData.gold = 1000;

        auto player = userEngine->CreatePlayer(humData);
        assert(player != nullptr);
        assert(player->GetCharName() == "TestPlayer");

        // 测试添加玩家
        bool added = userEngine->AddPlayer(player);
        assert(added == true);
        assert(userEngine->GetPlayerCount() == 1);

        // 测试查找玩家
        auto foundPlayer = userEngine->GetPlayer("TestPlayer");
        assert(foundPlayer == player);

        // 测试玩家登录
        bool loginResult = userEngine->PlayerLogin(player);
        assert(loginResult == true);

        // 测试玩家登出
        bool logoutResult = userEngine->PlayerLogout("TestPlayer");
        assert(logoutResult == true);
        assert(userEngine->GetPlayerCount() == 0);

        std::cout << "✓ 玩家管理功能测试通过" << std::endl;
    }

    static void TestEventHandlers() {
        std::cout << "测试事件处理器..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        userEngine->Initialize(mapManager, itemManager);

        // 初始化单例管理器
        PKManager::GetInstance().Initialize();
        GroupManager::GetInstance().Initialize();
        GuildManager::GetInstance().Initialize();

        // 创建测试玩家
        HumDataInfo humData;
        humData.charName = "EventTestPlayer";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;

        auto player = userEngine->CreatePlayer(humData);

        // 测试登录事件
        bool loginResult = userEngine->PlayerLogin(player);
        assert(loginResult == true);

        // 测试登出事件
        bool logoutResult = userEngine->PlayerLogout("EventTestPlayer");
        assert(logoutResult == true);

        std::cout << "✓ 事件处理器测试通过" << std::endl;
    }

    static void TestGMCommands() {
        std::cout << "测试GM命令..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        userEngine->Initialize(mapManager, itemManager);

        // 创建GM玩家
        HumDataInfo humData;
        humData.charName = "GMPlayer";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;

        auto player = userEngine->CreatePlayer(humData);
        player->SetPermission(10); // 设置为GM
        userEngine->AddPlayer(player);

        // 测试@level命令
        bool result = userEngine->ProcessGMCommand(player, "@level 50");
        assert(result == true);

        // 测试@gold命令
        result = userEngine->ProcessGMCommand(player, "@gold 10000");
        assert(result == true);

        // 测试@kick命令
        result = userEngine->ProcessGMCommand(player, "@kick NonExistentPlayer");
        assert(result == true);

        std::cout << "✓ GM命令测试通过" << std::endl;
    }

    static void TestPlayerProcessing() {
        std::cout << "测试玩家处理逻辑..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        userEngine->Initialize(mapManager, itemManager);

        // 创建测试玩家
        HumDataInfo humData;
        humData.charName = "ProcessTestPlayer";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;

        auto player = userEngine->CreatePlayer(humData);
        userEngine->AddPlayer(player);

        // 测试运行处理
        userEngine->Run();

        // 测试玩家处理
        userEngine->ProcessPlayers();

        std::cout << "✓ 玩家处理逻辑测试通过" << std::endl;
    }

    static void TestStatistics() {
        std::cout << "测试统计信息..." << std::endl;

        auto userEngine = std::make_unique<UserEngine>();
        auto mapManager = std::make_shared<MapManager>();
        auto itemManager = std::make_shared<ItemManager>();

        mapManager->Initialize("./maps");
        itemManager->Initialize("./data");
        userEngine->Initialize(mapManager, itemManager);

        // 获取初始统计
        auto stats = userEngine->GetStatistics();
        assert(stats.totalPlayers == 0);
        assert(stats.activePlayers == 0);

        // 添加玩家并检查统计
        HumDataInfo humData;
        humData.charName = "StatsTestPlayer";
        humData.job = JobType::WARRIOR;
        humData.gender = GenderType::MALE;
        humData.level = 1;

        auto player = userEngine->CreatePlayer(humData);
        userEngine->AddPlayer(player);

        stats = userEngine->GetStatistics();
        assert(stats.totalPlayers == 1);
        assert(stats.activePlayers == 1);

        std::cout << "✓ 统计信息测试通过" << std::endl;
    }
};

int main() {
    try {
        // 初始化日志系统
        Logger::SetLogFile("UserEngineTest.log");
        Logger::SetLogLevel(LogLevel::LOG_DEBUG);

        UserEngineTest::RunAllTests();
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
