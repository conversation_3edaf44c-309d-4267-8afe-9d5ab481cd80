@echo off
echo ===== 编译BaseObject功能完善测试 =====

cd /d "%~dp0"
set SRC_DIR=..\src
set TEST_FILE=test_baseobject_features.cpp

echo 正在编译测试文件...

g++ -std=c++17 ^
    -I%SRC_DIR% ^
    -I%SRC_DIR%\Common ^
    -I%SRC_DIR%\BaseObject ^
    -I%SRC_DIR%\GameEngine ^
    -DTEST_BUILD ^
    -o test_baseobject_features.exe ^
    %TEST_FILE% ^
    %SRC_DIR%\BaseObject\BaseObject.cpp ^
    %SRC_DIR%\BaseObject\PlayObject.cpp ^
    %SRC_DIR%\BaseObject\Monster.cpp ^
    %SRC_DIR%\GameEngine\PKManager.cpp ^
    %SRC_DIR%\GameEngine\GroupManager.cpp ^
    %SRC_DIR%\GameEngine\GuildManager.cpp ^
    %SRC_DIR%\Common\Logger.cpp ^
    %SRC_DIR%\Common\Utils.cpp

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
    echo 运行测试...
    test_baseobject_features.exe
) else (
    echo 编译失败！错误代码: %ERRORLEVEL%
)

pause
