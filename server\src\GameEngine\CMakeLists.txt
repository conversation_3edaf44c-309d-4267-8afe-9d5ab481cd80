# GameEngine CMakeLists.txt

# Add GameEngine executable
add_executable(GameEngine
    main.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    ActorManager.cpp
    MagicManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    GameEngine.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
    LocalDatabase.cpp
    CastleManager.cpp
)

# Add test executables
add_executable(GameEngineUnitTests
    test_core_features.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    ActorManager.cpp
    MagicManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    GameEngine.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
    LocalDatabase.cpp
    CastleManager.cpp
)

add_executable(GameEngineIntegrationTests
    CoreFeaturesTest.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    ActorManager.cpp
    MagicManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    GameEngine.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
)

add_executable(GameEnginePerformanceTests
    PerformanceTest.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    ActorManager.cpp
    MagicManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    GameEngine.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
)

add_executable(EnvironmentTests
    EnvironmentTest.cpp
    Environment.cpp
    ../Common/Logger.cpp
)

add_executable(SimpleTest
    SimpleTest.cpp
    Environment.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ../Common/Logger.cpp
)

add_executable(GroupSystemTest
    GroupSystemTest.cpp
    Environment.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(ItemUpgradeTest
    ItemUpgradeTest.cpp
    ItemManager.cpp
    ../Common/Logger.cpp
)

add_executable(ItemUpgradeExample
    ItemUpgradeExample.cpp
    ItemManager.cpp
    ../Common/Logger.cpp
)

add_executable(MinimalGuildTest
    MinimalGuildTest.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(BasicGuildTest
    BasicGuildTest.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(SimpleGuildTest
    SimpleGuildTest.cpp
    Environment.cpp
    StorageManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(GuildSystemExample
    GuildSystemExample.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    ActorManager.cpp
    MagicManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    GameEngine.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(CastleSystemExample
    CastleSystemExample.cpp
    CastleManager.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(SimpleCastleTest
    SimpleCastleTest.cpp
    CastleManager.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(MinimalCastleTest
    MinimalCastleTest.cpp
    CastleManager.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(QuickCastleTest
    QuickCastleTest.cpp
    CastleManager.cpp
    GuildManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

add_executable(SimpleUserEngineTest
    SimpleUserEngineTest.cpp
    UserEngine.cpp
    MapManager.cpp
    ItemManager.cpp
    Environment.cpp
    GameEngine.cpp
    ActorManager.cpp
    NPCManager.cpp
    MonsterManager.cpp
    MagicManager.cpp
    StorageManager.cpp
    TradeManager.cpp
    QuestManager.cpp
    MiniMapManager.cpp
    RepairManager.cpp
    PKManager.cpp
    GroupManager.cpp
    GuildManager.cpp
    GuildProtocolHandler.cpp
    ScriptEngine.cpp
    LocalDatabase.cpp
    CastleManager.cpp
    ../Common/Logger.cpp
    ../Common/Utils.cpp
)

# Include directories for all targets
foreach(target GameEngine GameEngineUnitTests GameEngineIntegrationTests GameEnginePerformanceTests EnvironmentTests SimpleTest GroupSystemTest ItemUpgradeTest ItemUpgradeExample MinimalGuildTest BasicGuildTest SimpleGuildTest GuildSystemExample CastleSystemExample SimpleCastleTest MinimalCastleTest QuickCastleTest SimpleUserEngineTest)
    target_include_directories(${target} PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
        ${CMAKE_SOURCE_DIR}/src
    )

    # Link libraries
    target_link_libraries(${target} PRIVATE
        Common
        Protocol
        BaseObject
        ${CMAKE_THREAD_LIBS_INIT}
    )

    # Platform-specific settings
    if(WIN32)
        target_link_libraries(${target} PRIVATE ws2_32)
    endif()

    # Set C++ standard
    set_target_properties(${target} PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
        CXX_EXTENSIONS OFF
    )
endforeach()

# Install targets
install(TARGETS GameEngine
    RUNTIME DESTINATION bin
)

# Test targets don't need to be installed, but we can optionally install them to a test directory
install(TARGETS GameEngineUnitTests GameEngineIntegrationTests GameEnginePerformanceTests EnvironmentTests
    RUNTIME DESTINATION test
    OPTIONAL
)