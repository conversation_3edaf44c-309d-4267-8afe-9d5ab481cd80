// SendRefMsgTest.cpp - SendRefMsg功能测试
#include "BaseObject.h"
#include "../Common/Logger.h"
#include "../Common/Utils.h"
#include "../Protocol/PacketTypes.h"
#include "../GameEngine/GameEngine.h"
#include "../GameEngine/Environment.h"
#include <iostream>
#include <vector>
#include <memory>
#include <cassert>

using namespace MirServer;

// 测试用的BaseObject子类
class TestPlayer : public BaseObject {
private:
    std::vector<std::string> m_receivedMessages;
    
public:
    TestPlayer(const std::string& name, int x, int y) {
        m_sCharName = name;
        m_currentPos.x = x;
        m_currentPos.y = y;
        m_mapName = "TestMap";
        m_btRaceServer = RC_PLAYOBJECT;
        m_observeMode = false;
        m_fixedHideMode = false;
    }
    
    // 重写SendMsg来记录接收到的消息
    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        std::string logMsg = "Player " + m_sCharName + " received message " + std::to_string(msgId) + 
                           " from " + (obj ? obj->GetCharName() : "unknown");
        m_receivedMessages.push_back(logMsg);
        std::cout << logMsg << std::endl;
    }
    
    ObjectType GetObjectType() const override {
        return ObjectType::Player;
    }
    
    const std::vector<std::string>& GetReceivedMessages() const {
        return m_receivedMessages;
    }
    
    void ClearMessages() {
        m_receivedMessages.clear();
    }
};

// 测试用的NPC类
class TestNPC : public BaseObject {
private:
    bool m_wantRefMsg;
    std::vector<std::string> m_receivedMessages;
    
public:
    TestNPC(const std::string& name, int x, int y, bool wantRefMsg = false) {
        m_sCharName = name;
        m_currentPos.x = x;
        m_currentPos.y = y;
        m_mapName = "TestMap";
        m_btRaceServer = RC_MONSTER;
        m_wantRefMsg = wantRefMsg;
    }
    
    void SendMsg(BaseObject* obj, WORD msgId, int param1, int param2, int param3, int param4, const std::string& msg) override {
        std::string logMsg = "NPC " + m_sCharName + " received message " + std::to_string(msgId) + 
                           " from " + (obj ? obj->GetCharName() : "unknown");
        m_receivedMessages.push_back(logMsg);
        std::cout << logMsg << std::endl;
    }
    
    ObjectType GetObjectType() const override {
        return ObjectType::Monster;
    }
    
    bool WantRefMsg() const override {
        return m_wantRefMsg;
    }
    
    const std::vector<std::string>& GetReceivedMessages() const {
        return m_receivedMessages;
    }
    
    void ClearMessages() {
        m_receivedMessages.clear();
    }
};

// 模拟Environment类
class MockEnvironment : public Environment {
private:
    std::vector<std::shared_ptr<BaseObject>> m_objects;
    
public:
    MockEnvironment(const std::string& mapName) : Environment(mapName) {}
    
    void AddObject(std::shared_ptr<BaseObject> obj) {
        m_objects.push_back(obj);
    }
    
    std::vector<std::shared_ptr<BaseObject>> GetObjectsAt(const Point& pos) override {
        std::vector<std::shared_ptr<BaseObject>> result;
        for (auto& obj : m_objects) {
            if (obj && obj->GetCurrentPos().x == pos.x && obj->GetCurrentPos().y == pos.y) {
                result.push_back(obj);
            }
        }
        return result;
    }
};

// 测试SendRefMsg基本功能
void TestBasicSendRefMsg() {
    std::cout << "\n=== 测试SendRefMsg基本功能 ===" << std::endl;
    
    // 创建测试对象
    auto sender = std::make_shared<TestPlayer>("Sender", 100, 100);
    auto receiver1 = std::make_shared<TestPlayer>("Receiver1", 105, 105); // 在范围内
    auto receiver2 = std::make_shared<TestPlayer>("Receiver2", 120, 120); // 超出范围
    auto npc1 = std::make_shared<TestNPC>("NPC1", 102, 102, true); // 想要接收RefMsg
    auto npc2 = std::make_shared<TestNPC>("NPC2", 103, 103, false); // 不想要接收RefMsg
    
    // 创建模拟环境
    auto mockEnv = std::make_shared<MockEnvironment>("TestMap");
    mockEnv->AddObject(sender);
    mockEnv->AddObject(receiver1);
    mockEnv->AddObject(receiver2);
    mockEnv->AddObject(npc1);
    mockEnv->AddObject(npc2);
    
    // 模拟GameEngine和EnvironmentManager
    // 注意：这里需要实际的GameEngine实现来支持
    
    // 发送RefMsg
    sender->SendRefMsg(RM_STRUCK, 0, 100, 500, 1000, "test message");
    
    // 验证结果
    // receiver1应该收到消息（在范围内）
    // receiver2不应该收到消息（超出范围）
    // npc1应该收到消息（想要接收且消息类型匹配）
    // npc2不应该收到消息（不想要接收）
    
    std::cout << "基本功能测试完成" << std::endl;
}

// 测试观察模式
void TestObserveMode() {
    std::cout << "\n=== 测试观察模式 ===" << std::endl;
    
    auto sender = std::make_shared<TestPlayer>("ObserveSender", 100, 100);
    auto receiver = std::make_shared<TestPlayer>("ObserveReceiver", 105, 105);
    
    // 设置观察模式
    // sender->SetObserveMode(true); // 需要添加这个方法
    
    // 发送RefMsg
    sender->SendRefMsg(RM_HEAR, 0, 0, 0, 0, "observe test");
    
    // 在观察模式下，消息应该只发送给自己
    std::cout << "观察模式测试完成" << std::endl;
}

// 测试缓存机制
void TestCacheMechanism() {
    std::cout << "\n=== 测试缓存机制 ===" << std::endl;
    
    auto sender = std::make_shared<TestPlayer>("CacheSender", 100, 100);
    
    // 第一次发送 - 应该扫描周围对象
    sender->SendRefMsg(RM_DEATH, 0, 0, 0, 0, "first message");
    
    // 立即第二次发送 - 应该使用缓存
    sender->SendRefMsg(RM_DEATH, 0, 0, 0, 0, "second message");
    
    // 等待500ms后发送 - 应该重新扫描
    // 这里需要实际的时间控制
    
    std::cout << "缓存机制测试完成" << std::endl;
}

int main() {
    std::cout << "SendRefMsg功能测试开始" << std::endl;
    
    try {
        // 初始化日志系统
        Logger::Initialize("SendRefMsgTest.log");
        
        // 运行测试
        TestBasicSendRefMsg();
        TestObserveMode();
        TestCacheMechanism();
        
        std::cout << "\n所有测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
