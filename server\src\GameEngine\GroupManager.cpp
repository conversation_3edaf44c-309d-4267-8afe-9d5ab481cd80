#include "GroupManager.h"
#include "GameEngine.h"
#include "../Common/Utils.h"
#include "../Common/Logger.h"
#include <algorithm>
#include <sstream>
#include <random>
#include <chrono>

namespace MirServer {

GroupManager& GroupManager::GetInstance() {
    static GroupManager instance;
    return instance;
}

bool GroupManager::Initialize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized) {
        return true;
    }

    // 清理数据
    m_groups.clear();
    m_playerGroups.clear();
    m_playerGroupModes.clear();
    m_pendingInvites.clear();

    m_initialized = true;
    Logger::Info("GroupManager initialized successfully");
    return true;
}

void GroupManager::Finalize() {
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized) {
        return;
    }

    // 解散所有组队
    for (auto& [groupId, group] : m_groups) {
        if (group && group->leader) {
            SendToGroupMembers(group, Protocol::SM_GROUPCANCEL, 0, 0, 0, "组队系统关闭，队伍已解散");
        }
    }

    m_groups.clear();
    m_playerGroups.clear();
    m_playerGroupModes.clear();
    m_pendingInvites.clear();

    m_initialized = false;
    Logger::Info("GroupManager finalized");
}

bool GroupManager::CreateGroup(PlayObject* leader) {
    if (!leader || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string playerName = leader->GetCharName();

    // 检查是否已经在组队中
    if (m_playerGroups.find(playerName) != m_playerGroups.end()) {
        leader->SendMessage("您已经在队伍中", 0);
        return false;
    }

    // 生成组队ID
    std::string groupId = GenerateGroupId();

    // 创建组队信息
    auto group = std::make_shared<GroupInfo>();
    group->groupId = groupId;
    group->leader = std::shared_ptr<PlayObject>(leader, [](PlayObject*) {}); // 不删除，由其他地方管理
    group->members.push_back(group->leader);
    group->createTime = GetCurrentTime();

    // 添加到管理器
    m_groups[groupId] = group;
    m_playerGroups[playerName] = groupId;

    // 设置玩家的组队信息
    leader->SetGroupOwner(leader);

    // 发送创建成功消息
    leader->SendDefMessage(Protocol::SM_GROUPMEMBERS, 1, 0, 0, 0);
    leader->SendMessage("队伍创建成功，您是队长", 0);

    Logger::Info("Group created: " + groupId + " by " + playerName);
    return true;
}

bool GroupManager::JoinGroup(PlayObject* player, const std::string& groupId) {
    if (!player || groupId.empty() || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string playerName = player->GetCharName();

    // 检查是否已经在组队中
    if (m_playerGroups.find(playerName) != m_playerGroups.end()) {
        player->SendMessage("您已经在队伍中", 0);
        return false;
    }

    // 查找目标组队
    auto it = m_groups.find(groupId);
    if (it == m_groups.end()) {
        player->SendMessage("队伍不存在", 0);
        return false;
    }

    auto group = it->second;
    if (!group) {
        player->SendMessage("队伍信息错误", 0);
        return false;
    }

    // 检查队伍是否已满
    if (static_cast<int>(group->members.size()) >= group->maxMembers) {
        player->SendMessage("队伍已满", 0);
        return false;
    }

    // 检查是否允许加入
    if (!group->allowJoin) {
        player->SendMessage("队伍不允许加入", 0);
        return false;
    }

    // 添加到队伍
    auto playerPtr = std::shared_ptr<PlayObject>(player, [](PlayObject*) {});
    group->members.push_back(playerPtr);
    m_playerGroups[playerName] = groupId;

    // 设置玩家的组队信息
    if (group->leader) {
        player->SetGroupOwner(group->leader.get());
    }

    // 通知所有队员
    std::string joinMsg = playerName + " 加入了队伍";
    SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 0, 0, joinMsg);

    Logger::Info("Player " + playerName + " joined group " + groupId);
    return true;
}

bool GroupManager::LeaveGroup(PlayObject* player) {
    if (!player || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string playerName = player->GetCharName();

    // 查找玩家的组队
    auto it = m_playerGroups.find(playerName);
    if (it == m_playerGroups.end()) {
        player->SendMessage("您不在任何队伍中", 0);
        return false;
    }

    std::string groupId = it->second;
    auto groupIt = m_groups.find(groupId);
    if (groupIt == m_groups.end()) {
        m_playerGroups.erase(it);
        return false;
    }

    auto group = groupIt->second;
    if (!group) {
        m_playerGroups.erase(it);
        return false;
    }

    // 从队伍中移除玩家
    RemovePlayerFromGroup(player, group);

    // 清理玩家的组队信息
    player->SetGroupOwner(nullptr);
    m_playerGroups.erase(it);

    // 发送离开消息
    player->SendDefMessage(Protocol::SM_GROUPCANCEL, 0, 0, 0, 0);
    player->SendMessage("您已离开队伍", 0);

    // 通知其他队员
    if (!group->members.empty()) {
        std::string leaveMsg = playerName + " 离开了队伍";
        SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 0, 0, leaveMsg);
    }

    // 检查是否需要更新队长或解散队伍
    if (group->leader.get() == player) {
        UpdateGroupLeader(group);
    }

    // 清理空队伍
    CleanupEmptyGroups();

    Logger::Info("Player " + playerName + " left group " + groupId);
    return true;
}

std::string GroupManager::GenerateGroupId() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(100000, 999999);

    std::string groupId;
    do {
        groupId = "GROUP_" + std::to_string(dis(gen));
    } while (m_groups.find(groupId) != m_groups.end());

    return groupId;
}

bool GroupManager::ValidateGroupOperation(PlayObject* player, const std::string& operation) const {
    if (!player || !m_initialized) {
        return false;
    }

    // 检查玩家是否在线
    if (!player->IsOnline()) {
        return false;
    }

    return true;
}

void GroupManager::RemovePlayerFromGroup(PlayObject* player, std::shared_ptr<GroupInfo> group) {
    if (!player || !group) {
        return;
    }

    // 从成员列表中移除
    auto it = std::find_if(group->members.begin(), group->members.end(),
        [player](const std::shared_ptr<PlayObject>& ptr) {
            return ptr.get() == player;
        });

    if (it != group->members.end()) {
        group->members.erase(it);
    }
}

void GroupManager::UpdateGroupLeader(std::shared_ptr<GroupInfo> group) {
    if (!group || group->members.empty()) {
        return;
    }

    // 选择第一个有效成员作为新队长
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            group->leader = member;

            // 更新所有成员的组队信息
            for (auto& ptr : group->members) {
                if (ptr) {
                    ptr->SetGroupOwner(member.get());
                }
            }

            // 通知新队长
            std::string msg = "您已成为新的队长";
            SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 1, 0, msg);

            Logger::Info("Group " + group->groupId + " leader changed to " + member->GetCharName());
            return;
        }
    }

    // 如果没有有效成员，标记为需要清理
    group->leader.reset();
}

void GroupManager::CleanupEmptyGroups() {
    auto it = m_groups.begin();
    while (it != m_groups.end()) {
        auto& group = it->second;
        if (!group || group->members.empty() || !group->leader) {
            Logger::Info("Cleaning up empty group: " + it->first);
            it = m_groups.erase(it);
        } else {
            ++it;
        }
    }
}

bool GroupManager::KickMember(PlayObject* leader, const std::string& memberName) {
    if (!leader || memberName.empty() || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否是队长
    if (!IsGroupLeader(leader)) {
        leader->SendMessage("只有队长才能踢出队员", 0);
        return false;
    }

    auto group = GetPlayerGroup(leader);
    if (!group) {
        return false;
    }

    // 查找要踢出的成员
    PlayObject* targetPlayer = nullptr;
    for (auto& member : group->members) {
        if (member && member->GetCharName() == memberName) {
            targetPlayer = member.get();
            break;
        }
    }

    if (!targetPlayer) {
        leader->SendMessage("队员不存在", 0);
        return false;
    }

    if (targetPlayer == leader) {
        leader->SendMessage("不能踢出自己", 0);
        return false;
    }

    // 移除成员
    RemovePlayerFromGroup(targetPlayer, group);

    // 清理玩家的组队信息
    targetPlayer->SetGroupOwner(nullptr);
    m_playerGroups.erase(memberName);

    // 发送消息
    targetPlayer->SendDefMessage(Protocol::SM_GROUPCANCEL, 0, 0, 0, 0);
    targetPlayer->SendMessage("您被踢出了队伍", 0);

    std::string kickMsg = memberName + " 被踢出了队伍";
    SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 0, 0, kickMsg);

    Logger::Info("Player " + memberName + " was kicked from group " + group->groupId + " by " + leader->GetCharName());
    return true;
}

bool GroupManager::DisbandGroup(PlayObject* leader) {
    if (!leader || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查是否是队长
    if (!IsGroupLeader(leader)) {
        leader->SendMessage("只有队长才能解散队伍", 0);
        return false;
    }

    auto group = GetPlayerGroup(leader);
    if (!group) {
        return false;
    }

    std::string groupId = group->groupId;

    // 通知所有成员
    SendToGroupMembers(group, Protocol::SM_GROUPCANCEL, 0, 0, 0, "队伍已解散");

    // 清理所有成员的组队信息
    for (auto& member : group->members) {
        if (member) {
            member->SetGroupOwner(nullptr);
            m_playerGroups.erase(member->GetCharName());
        }
    }

    // 移除组队
    m_groups.erase(groupId);

    Logger::Info("Group " + groupId + " disbanded by " + leader->GetCharName());
    return true;
}

bool GroupManager::ChangeLeader(PlayObject* currentLeader, PlayObject* newLeader) {
    if (!currentLeader || !newLeader || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查当前玩家是否是队长
    if (!IsGroupLeader(currentLeader)) {
        currentLeader->SendMessage("只有队长才能转让队长", 0);
        return false;
    }

    auto group = GetPlayerGroup(currentLeader);
    if (!group) {
        return false;
    }

    // 检查新队长是否在同一队伍中
    if (!IsGroupMember(currentLeader, newLeader)) {
        currentLeader->SendMessage("目标玩家不在队伍中", 0);
        return false;
    }

    // 查找新队长在成员列表中的位置
    std::shared_ptr<PlayObject> newLeaderPtr;
    for (auto& member : group->members) {
        if (member && member.get() == newLeader) {
            newLeaderPtr = member;
            break;
        }
    }

    if (!newLeaderPtr) {
        currentLeader->SendMessage("目标玩家不在队伍中", 0);
        return false;
    }

    // 更换队长
    group->leader = newLeaderPtr;

    // 更新所有成员的组队信息
    for (auto& member : group->members) {
        if (member) {
            member->SetGroupOwner(newLeader);
        }
    }

    // 通知所有成员队长变更
    std::string changeMsg = newLeader->GetCharName() + " 成为新的队长";
    SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 1, 0, changeMsg);

    Logger::Info("Group " + group->groupId + " leader changed from " + currentLeader->GetCharName() + " to " + newLeader->GetCharName());
    return true;
}

bool GroupManager::InvitePlayer(PlayObject* inviter, PlayObject* target) {
    if (!inviter || !target || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string inviterName = inviter->GetCharName();
    std::string targetName = target->GetCharName();

    // 检查邀请者是否在队伍中
    auto inviterGroup = GetPlayerGroup(inviter);
    if (!inviterGroup) {
        inviter->SendMessage("您不在任何队伍中", 0);
        return false;
    }

    // 检查是否是队长
    if (!IsGroupLeader(inviter)) {
        inviter->SendMessage("只有队长才能邀请队员", 0);
        return false;
    }

    // 检查目标是否已在队伍中
    if (IsInGroup(target)) {
        inviter->SendMessage(targetName + " 已经在队伍中", 0);
        return false;
    }

    // 检查队伍是否已满
    if (static_cast<int>(inviterGroup->members.size()) >= inviterGroup->maxMembers) {
        inviter->SendMessage("队伍已满", 0);
        return false;
    }

    // 检查目标的组队模式
    GroupMode targetMode = GetGroupMode(target);
    if (targetMode == GroupMode::NONE) {
        inviter->SendMessage(targetName + " 拒绝组队", 0);
        return false;
    }

    // 检查是否已有待处理的邀请
    for (const auto& invite : m_pendingInvites) {
        if (invite.targetName == targetName && invite.inviterName == inviterName) {
            inviter->SendMessage("已经向 " + targetName + " 发送了邀请", 0);
            return false;
        }
    }

    // 创建邀请
    GroupInvite invite;
    invite.inviterName = inviterName;
    invite.targetName = targetName;
    invite.groupId = inviterGroup->groupId;
    invite.inviteTime = GetCurrentTime();
    invite.expireTime = invite.inviteTime + INVITE_EXPIRE_TIME;

    m_pendingInvites.push_back(invite);

    // 发送邀请消息
    inviter->SendMessage("已向 " + targetName + " 发送组队邀请", 0);
    target->SendDefMessage(Protocol::SM_GROUPINVITE, 0, 0, 0, 0);
    target->SendMessage(inviterName + " 邀请您加入队伍，输入 @允许组队 接受邀请", 0);

    Logger::Info("Group invite sent from " + inviterName + " to " + targetName);
    return true;
}

bool GroupManager::AcceptInvite(PlayObject* player, const std::string& inviterName) {
    if (!player || inviterName.empty() || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string playerName = player->GetCharName();

    // 查找邀请
    auto it = std::find_if(m_pendingInvites.begin(), m_pendingInvites.end(),
        [&](const GroupInvite& invite) {
            return invite.targetName == playerName && invite.inviterName == inviterName;
        });

    if (it == m_pendingInvites.end()) {
        player->SendMessage("没有来自 " + inviterName + " 的组队邀请", 0);
        return false;
    }

    // 检查邀请是否过期
    if (GetCurrentTime() > it->expireTime) {
        player->SendMessage("组队邀请已过期", 0);
        m_pendingInvites.erase(it);
        return false;
    }

    std::string groupId = it->groupId;
    m_pendingInvites.erase(it);

    // 加入队伍
    return JoinGroup(player, groupId);
}

bool GroupManager::RejectInvite(PlayObject* player, const std::string& inviterName) {
    if (!player || inviterName.empty() || !m_initialized) {
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string playerName = player->GetCharName();

    // 查找并移除邀请
    auto it = std::find_if(m_pendingInvites.begin(), m_pendingInvites.end(),
        [&](const GroupInvite& invite) {
            return invite.targetName == playerName && invite.inviterName == inviterName;
        });

    if (it == m_pendingInvites.end()) {
        player->SendMessage("没有来自 " + inviterName + " 的组队邀请", 0);
        return false;
    }

    m_pendingInvites.erase(it);
    player->SendMessage("已拒绝 " + inviterName + " 的组队邀请", 0);

    Logger::Info("Group invite rejected by " + playerName + " from " + inviterName);
    return true;
}

std::shared_ptr<GroupInfo> GroupManager::GetPlayerGroup(PlayObject* player) const {
    if (!player || !m_initialized) {
        return nullptr;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_playerGroups.find(player->GetCharName());
    if (it == m_playerGroups.end()) {
        return nullptr;
    }

    auto groupIt = m_groups.find(it->second);
    if (groupIt == m_groups.end()) {
        return nullptr;
    }

    return groupIt->second;
}

std::shared_ptr<GroupInfo> GroupManager::GetGroup(const std::string& groupId) const {
    if (groupId.empty() || !m_initialized) {
        return nullptr;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_groups.find(groupId);
    return (it != m_groups.end()) ? it->second : nullptr;
}

bool GroupManager::IsInGroup(PlayObject* player) const {
    return GetPlayerGroup(player) != nullptr;
}

bool GroupManager::IsGroupLeader(PlayObject* player) const {
    auto group = GetPlayerGroup(player);
    return group && group->leader.get() == player;
}

bool GroupManager::IsGroupMember(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    auto group1 = GetPlayerGroup(player1);
    auto group2 = GetPlayerGroup(player2);

    return group1 && group2 && group1->groupId == group2->groupId;
}

std::vector<std::shared_ptr<PlayObject>> GroupManager::GetGroupMembers(PlayObject* player) const {
    std::vector<std::shared_ptr<PlayObject>> members;

    auto group = GetPlayerGroup(player);
    if (!group) {
        return members;
    }

    for (auto& member : group->members) {
        if (member) {
            members.push_back(member);
        }
    }

    return members;
}

void GroupManager::ShareExperience(PlayObject* player, DWORD totalExp) {
    if (!player || totalExp == 0 || !m_initialized) {
        return;
    }

    auto group = GetPlayerGroup(player);
    if (!group || group->members.size() <= 1) {
        // 单人或无组队，直接获得经验
        player->GainExpDirect(totalExp);
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 收集在范围内且等级差异合理的成员
    std::vector<std::shared_ptr<PlayObject>> validMembers;
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            // 检查距离和等级差异
            if (IsInExpRange(player, member.get()) && IsLevelDiffValid(player, member.get())) {
                validMembers.push_back(member);
            }
        }
    }

    if (validMembers.empty()) {
        // 没有有效成员，给原玩家经验
        player->GainExpDirect(totalExp);
        return;
    }

    // 根据分配模式计算经验
    if (group->expShareMode == ExpShareMode::EQUAL) {
        // 平均分配
        DWORD expPerMember = totalExp / validMembers.size();
        for (auto& member : validMembers) {
            member->GainExpDirect(expPerMember);

            // 发送经验获得消息
            std::string expMsg = "组队经验: +" + std::to_string(expPerMember);
            member->SendMessage(expMsg, 0);
        }
    } else {
        // 按等级分配
        DWORD totalLevel = 0;
        for (auto& member : validMembers) {
            totalLevel += member->GetLevel();
        }

        if (totalLevel > 0) {
            for (auto& member : validMembers) {
                DWORD memberExp = (totalExp * member->GetLevel()) / totalLevel;
                member->GainExpDirect(memberExp);

                // 发送经验获得消息
                std::string expMsg = "组队经验: +" + std::to_string(memberExp);
                member->SendMessage(expMsg, 0);
            }
        }
    }

    Logger::Debug("Shared " + std::to_string(totalExp) + " experience among " +
                 std::to_string(validMembers.size()) + " group members");
}

std::vector<std::pair<std::shared_ptr<PlayObject>, DWORD>> GroupManager::CalculateExpShare(
    std::shared_ptr<GroupInfo> group, DWORD totalExp) const {

    std::vector<std::pair<std::shared_ptr<PlayObject>, DWORD>> result;

    if (!group || group->members.empty()) {
        return result;
    }

    // 收集在线的成员
    std::vector<std::shared_ptr<PlayObject>> validMembers;
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            // 暂时只检查在线状态，距离检查在ShareExperience中进行
            validMembers.push_back(member);
        }
    }

    if (validMembers.empty()) {
        return result;
    }

    // 根据分配模式计算经验
    if (group->expShareMode == ExpShareMode::EQUAL) {
        // 平均分配
        DWORD expPerMember = totalExp / validMembers.size();
        for (auto& member : validMembers) {
            result.emplace_back(member, expPerMember);
        }
    } else {
        // 按等级分配
        DWORD totalLevel = 0;
        for (auto& member : validMembers) {
            totalLevel += member->GetLevel();
        }

        if (totalLevel > 0) {
            for (auto& member : validMembers) {
                DWORD memberExp = (totalExp * member->GetLevel()) / totalLevel;
                result.emplace_back(member, memberExp);
            }
        }
    }

    return result;
}

void GroupManager::SetExpShareMode(PlayObject* leader, ExpShareMode mode) {
    if (!leader || !m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    if (!IsGroupLeader(leader)) {
        leader->SendMessage("只有队长才能设置经验分配模式", 0);
        return;
    }

    auto group = GetPlayerGroup(leader);
    if (!group) {
        return;
    }

    group->expShareMode = mode;

    std::string modeStr = (mode == ExpShareMode::EQUAL) ? "平均分配" : "按等级分配";
    std::string msg = "经验分配模式已设置为: " + modeStr;
    SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, 0, static_cast<WORD>(mode), 0, msg);

    Logger::Info("Group " + group->groupId + " exp share mode changed to " + std::to_string(static_cast<int>(mode)));
}

void GroupManager::SendGroupMessage(PlayObject* sender, const std::string& message) {
    if (!sender || message.empty() || !m_initialized) {
        return;
    }

    auto group = GetPlayerGroup(sender);
    if (!group) {
        sender->SendMessage("您不在任何队伍中", 0);
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    std::string fullMessage = "[队伍] " + sender->GetCharName() + ": " + message;

    // 发送给所有队员
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            member->SendMessage(fullMessage, 11); // 11 = 组队聊天颜色
        }
    }

    Logger::Debug("Group message from " + sender->GetCharName() + ": " + message);
}

void GroupManager::SetGroupMode(PlayObject* player, GroupMode mode) {
    if (!player || !m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    m_playerGroupModes[player->GetCharName()] = mode;

    std::string modeStr;
    switch (mode) {
        case GroupMode::NONE:
            modeStr = "拒绝组队";
            break;
        case GroupMode::GROUP:
            modeStr = "组队模式";
            break;
        case GroupMode::ALLOW_GROUP:
            modeStr = "允许组队";
            break;
    }

    player->SendMessage("组队模式已设置为: " + modeStr, 0);
    Logger::Debug("Player " + player->GetCharName() + " group mode set to " + std::to_string(static_cast<int>(mode)));
}

GroupMode GroupManager::GetGroupMode(PlayObject* player) const {
    if (!player || !m_initialized) {
        return GroupMode::NONE;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    auto it = m_playerGroupModes.find(player->GetCharName());
    return (it != m_playerGroupModes.end()) ? it->second : GroupMode::ALLOW_GROUP;
}

void GroupManager::SendGroupInfo(PlayObject* player) {
    if (!player || !m_initialized) {
        return;
    }

    auto group = GetPlayerGroup(player);
    if (!group) {
        player->SendDefMessage(Protocol::SM_GROUPCANCEL, 0, 0, 0, 0);
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 发送组队基本信息
    WORD memberCount = static_cast<WORD>(group->members.size());
    WORD isLeader = IsGroupLeader(player) ? 1 : 0;
    player->SendDefMessage(Protocol::SM_GROUPMEMBERS, memberCount, isLeader, 0, 0);

    // 发送成员列表
    SendGroupMemberList(player);
}

void GroupManager::SendGroupMemberList(PlayObject* player) {
    if (!player || !m_initialized) {
        return;
    }

    auto group = GetPlayerGroup(player);
    if (!group) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 构建成员列表字符串
    std::string memberList;
    for (size_t i = 0; i < group->members.size(); ++i) {
        auto& member = group->members[i];
        if (member) {
            if (i > 0) memberList += "|";

            std::string memberInfo = member->GetCharName();
            memberInfo += "," + std::to_string(member->GetLevel());
            memberInfo += "," + std::to_string(static_cast<int>(member->GetJob()));
            memberInfo += ",";
            memberInfo += (member->IsOnline() ? "1" : "0");
            memberInfo += ",";
            memberInfo += (member.get() == group->leader.get() ? "1" : "0");

            memberList += memberInfo;
        }
    }

    // 发送成员列表
    player->SendDefMessage(Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 0, 0, 0);
    if (!memberList.empty()) {
        player->SendMessage(memberList, 0);
    }
}

void GroupManager::NotifyGroupChange(const std::string& groupId, PlayObject* excludePlayer) {
    if (groupId.empty() || !m_initialized) {
        return;
    }

    auto group = GetGroup(groupId);
    if (!group) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 通知所有成员更新组队信息
    for (auto& member : group->members) {
        if (member && member.get() != excludePlayer && member->IsOnline()) {
            SendGroupInfo(member.get());
        }
    }
}

void GroupManager::Run() {
    if (!m_initialized) {
        return;
    }

    ProcessInvites();
    CheckGroupStatus();
    CleanupEmptyGroups();
}

void GroupManager::ProcessInvites() {
    if (!m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    DWORD currentTime = GetCurrentTime();

    // 移除过期的邀请
    auto it = m_pendingInvites.begin();
    while (it != m_pendingInvites.end()) {
        if (currentTime > it->expireTime) {
            Logger::Debug("Group invite expired: " + it->inviterName + " -> " + it->targetName);
            it = m_pendingInvites.erase(it);
        } else {
            ++it;
        }
    }
}

void GroupManager::CheckGroupStatus() {
    if (!m_initialized) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 检查所有组队的状态
    for (auto& [groupId, group] : m_groups) {
        if (!group) continue;

        // 移除离线的成员
        auto it = group->members.begin();
        while (it != group->members.end()) {
            if (*it) {
                if (!(*it)->IsOnline()) {
                    std::string memberName = (*it)->GetCharName();
                    m_playerGroups.erase(memberName);
                    it = group->members.erase(it);

                    // 通知其他成员
                    std::string msg = memberName + " 离线，已离开队伍";
                    SendToGroupMembers(group, Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 0, 0, msg);

                    Logger::Info("Removed offline player " + memberName + " from group " + groupId);
                } else {
                    ++it;
                }
            } else {
                it = group->members.erase(it);
            }
        }

        // 检查队长是否有效
        if (group->leader && !group->leader->IsOnline()) {
            UpdateGroupLeader(group);
        }
    }
}

void GroupManager::OnPlayerLogout(PlayObject* player) {
    if (!player || !m_initialized) {
        return;
    }

    // 玩家离线时不立即移除，而是在CheckGroupStatus中处理
    // 这样可以给玩家一些时间重新连接
    Logger::Debug("Player " + player->GetCharName() + " logged out, group status will be checked later");
}

void GroupManager::OnPlayerLogin(PlayObject* player) {
    if (!player || !m_initialized) {
        return;
    }

    // 玩家上线时发送组队信息
    SendGroupInfo(player);

    Logger::Debug("Player " + player->GetCharName() + " logged in, sent group info");
}

void GroupManager::SendToGroupMembers(std::shared_ptr<GroupInfo> group, WORD msgType,
                                     WORD param1, WORD param2, WORD param3,
                                     const std::string& data, PlayObject* excludePlayer) {
    if (!group) {
        return;
    }

    for (auto& member : group->members) {
        if (member && member.get() != excludePlayer && member->IsOnline()) {
            member->SendDefMessage(msgType, param1, param2, param3, 0);
            if (!data.empty()) {
                member->SendMessage(data, 0);
            }
        }
    }
}

// 高级功能实现
bool GroupManager::IsInExpRange(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    // 检查是否在同一地图
    if (player1->GetMapName() != player2->GetMapName()) {
        return false;
    }

    // 检查距离
    Point pos1 = player1->GetCurrentPos();
    Point pos2 = player2->GetCurrentPos();

    int distance = std::abs(pos1.x - pos2.x) + std::abs(pos1.y - pos2.y);
    return distance <= GROUP_EXP_RANGE;
}

bool GroupManager::IsLevelDiffValid(PlayObject* player1, PlayObject* player2) const {
    if (!player1 || !player2) {
        return false;
    }

    int level1 = player1->GetLevel();
    int level2 = player2->GetLevel();

    return std::abs(level1 - level2) <= MAX_LEVEL_DIFF;
}

void GroupManager::UpdateGroupPositions(const std::string& groupId) {
    if (groupId.empty() || !m_initialized) {
        return;
    }

    auto group = GetGroup(groupId);
    if (!group) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 向所有成员发送位置更新
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            SendGroupPositionUpdate(member.get());
        }
    }
}

void GroupManager::SendGroupPositionUpdate(PlayObject* player) {
    if (!player || !m_initialized) {
        return;
    }

    auto group = GetPlayerGroup(player);
    if (!group) {
        return;
    }

    // 构建位置信息字符串
    std::string positionData;
    for (size_t i = 0; i < group->members.size(); ++i) {
        auto& member = group->members[i];
        if (member && member->IsOnline()) {
            if (i > 0) positionData += "|";

            Point pos = member->GetCurrentPos();
            positionData += member->GetCharName();
            positionData += "," + member->GetMapName();
            positionData += "," + std::to_string(pos.x);
            positionData += "," + std::to_string(pos.y);
        }
    }

    // 发送位置更新消息
    player->SendDefMessage(Protocol::SM_GROUPMEMBERS, static_cast<WORD>(group->members.size()), 1, 0, 0);
    if (!positionData.empty()) {
        player->SendMessage(positionData, 0);
    }
}

bool GroupManager::CanEnterDungeon(PlayObject* player, const std::string& dungeonName) const {
    if (!player || dungeonName.empty() || !m_initialized) {
        return false;
    }

    auto group = GetPlayerGroup(player);
    if (!group) {
        // 单人可以进入某些副本
        return true;
    }

    // 检查所有队员是否满足副本要求
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            // 检查等级要求
            if (member->GetLevel() < 20) { // 假设副本最低等级要求
                return false;
            }

            // 检查是否在同一地图
            if (member->GetMapName() != player->GetMapName()) {
                return false;
            }
        }
    }

    return true;
}

void GroupManager::TeleportGroupToLeader(PlayObject* leader) {
    if (!leader || !m_initialized) {
        return;
    }

    if (!IsGroupLeader(leader)) {
        leader->SendMessage("只有队长才能召集队员", 0);
        return;
    }

    auto group = GetPlayerGroup(leader);
    if (!group) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    Point leaderPos = leader->GetCurrentPos();
    std::string leaderMap = leader->GetMapName();

    // 传送所有队员到队长位置
    for (auto& member : group->members) {
        if (member && member.get() != leader && member->IsOnline()) {
            // 检查是否可以传送
            if (member->GetMapName() != leaderMap) {
                member->SpaceMove(leaderMap, leaderPos.x, leaderPos.y);
                member->SendMessage("已被队长召集", 0);
            }
        }
    }

    leader->SendMessage("已召集所有队员", 0);
    Logger::Info("Group leader " + leader->GetCharName() + " teleported group to position");
}

void GroupManager::TeleportGroupToPosition(PlayObject* leader, const std::string& mapName, int x, int y) {
    if (!leader || mapName.empty() || !m_initialized) {
        return;
    }

    if (!IsGroupLeader(leader)) {
        leader->SendMessage("只有队长才能传送队伍", 0);
        return;
    }

    auto group = GetPlayerGroup(leader);
    if (!group) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 传送所有队员到指定位置
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            member->SpaceMove(mapName, x, y);
            member->SendMessage("队伍已传送到 " + mapName, 0);
        }
    }

    Logger::Info("Group " + group->groupId + " teleported to " + mapName + " (" + std::to_string(x) + "," + std::to_string(y) + ")");
}

int GroupManager::GetGroupAverageLevel(const std::string& groupId) const {
    if (groupId.empty() || !m_initialized) {
        return 0;
    }

    auto group = GetGroup(groupId);
    if (!group || group->members.empty()) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    int totalLevel = 0;
    int memberCount = 0;

    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            totalLevel += member->GetLevel();
            memberCount++;
        }
    }

    return memberCount > 0 ? totalLevel / memberCount : 0;
}

int GroupManager::GetGroupTotalLevel(const std::string& groupId) const {
    if (groupId.empty() || !m_initialized) {
        return 0;
    }

    auto group = GetGroup(groupId);
    if (!group || group->members.empty()) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    int totalLevel = 0;
    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            totalLevel += member->GetLevel();
        }
    }

    return totalLevel;
}

std::vector<std::string> GroupManager::GetGroupMemberNames(const std::string& groupId) const {
    std::vector<std::string> names;

    if (groupId.empty() || !m_initialized) {
        return names;
    }

    auto group = GetGroup(groupId);
    if (!group || group->members.empty()) {
        return names;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    for (auto& member : group->members) {
        if (member && member->IsOnline()) {
            names.push_back(member->GetCharName());
        }
    }

    return names;
}

} // namespace MirServer
